// Station Details Page - Shows detailed information about a charging station
// This page is divided into three main sections:
// 1. Station Information - Header, station details, etc.
// 2. Connector Selection - Connector cards, connector groups
// 3. Bottom Bar and Actions - Charge button, connector selection

import 'dart:async';
import 'dart:convert';
import 'package:ecoplug/models/evse.dart';
import 'package:ecoplug/models/station.dart';
import 'package:ecoplug/models/station/station_details_response.dart'
    as station_details;
import 'package:ecoplug/screens/station/charging_options_page.dart';
import 'package:ecoplug/screens/charging_initialization_screen.dart';
// import 'package:ecoplug/screens/analytics/connector_analytics_page.dart'; // Temporarily disabled - analytics feature
import 'package:ecoplug/services/api_bridge.dart';
import 'package:ecoplug/services/gun_connection_service.dart';
import 'package:ecoplug/services/review_service.dart';
import 'package:ecoplug/services/charging_parameters_service.dart';
import 'package:ecoplug/services/charging_session_service.dart';
import 'package:ecoplug/utils/app_themes.dart';
import 'package:ecoplug/services/token_service.dart';
// REMOVED: StationLoadingAnimation import - PERMANENTLY ELIMINATED
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:line_icons/line_icons.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:share_plus/share_plus.dart';
import 'package:ecoplug/utils/connector_utils.dart';
import 'package:ecoplug/widgets/station_loading_animation.dart';
import 'package:ecoplug/core/api/api_service.dart';
import 'package:ecoplug/core/api/api_config.dart';
import 'package:ecoplug/services/connectivity_service.dart';
import 'package:ecoplug/services/connectivity_error_service.dart';
import 'package:ecoplug/services/global_connectivity_monitor.dart';
import 'package:ecoplug/screens/error/connectivity_error_page.dart';
import 'package:ecoplug/utils/color_utils.dart';

// Removed debug logging to clean up output for charging issue investigation

class StationDetailsPage extends StatefulWidget {
  final String uid; // UID is required for API calls
  final Station? station; // Optional station object

  const StationDetailsPage({
    super.key,
    required this.uid,
    this.station,
  });

  @override
  State<StationDetailsPage> createState() => _StationDetailsPageState();
}

class _StationDetailsPageState extends State<StationDetailsPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  int _selectedTabIndex = 0;
  final _apiBridge = ApiBridge();
  bool _isLoading = true; // Added for loading page

  // State variables
  // REMOVED: bool _isLoading = true; - PERMANENTLY ELIMINATED TO PREVENT INFINITE LOADING
  bool _isLoadingReviews = false;
  String? _errorMessage;
  Station? _station;
  station_details.StationDetailsData?
      _rawStationData; // Store raw API data for lastUsed access
  station_details.Connector? _selectedConnector;
  List<dynamic> _reviews = [];

  // Wallet and instant charging state
  station_details.Wallet? _walletData;
  int? _instantCharging;

  // Background refresh mechanism (simplified without isolates)
  Timer? _backgroundRefreshTimer;
  bool _isBackgroundRefreshActive = false;
  station_details.StationDetailsResponse? _lastKnownData;

  // 🚨 NEW: Race condition prevention for charging flow
  bool _isChargingFlowActive = false;

  // 🚨 NEW: Prevent multiple simultaneous charging button taps
  bool _isChargingButtonProcessing = false;

  // Local cache for connectors to avoid repeated API calls
  static final Map<String, List<station_details.Connector>> _connectorsCache =
      {};

  // Map to store EVSE power output types for connector power type display
  final Map<String, String> _evsePowerOutputMap = {};

  // Snackbar management for connector status
  bool _isSnackbarVisible = false;
  Timer? _snackbarTimer;

  // Gun connection detection state
  final Map<String, bool> _gunConnectionStatus =
      {}; // connectorId -> isConnected
  final Map<String, StreamSubscription> _gunConnectionSubscriptions = {};
  final GunConnectionService _gunConnectionService = GunConnectionService();

  // Review service
  final ReviewService _reviewService = ReviewService();

  // Bookmark functionality state
  bool _isBookmarked = false;
  bool _isBookmarkLoading = false;
  final ApiService _apiService = ApiService();

  // Unified badge color for consistent design across all badge types
  static const Color _unifiedBadgeColor = Color(0xFF1E88E5); // Blue color for all badges

  // Use centralized app theme color for charging status
  static final Color _chargingLimeGreen = AppThemes.primaryColor; // Use app theme color for charging connectors

  // Helper methods for state management
  void _updateErrorState(String message) {
    if (mounted) {
      setState(() {
        _errorMessage = message;
        _isLoading = false; // Ensure loading stops on error
        _isLoadingReviews = false;
      });
    }
  }

  // Bookmark functionality methods
  /// Toggle bookmark status with optimistic UI updates
  Future<void> _toggleBookmark() async {
    if (_isBookmarkLoading || _station?.uid == null) return;

    debugPrint('\n=== BOOKMARK TOGGLE ===');
    debugPrint('Current bookmark status: $_isBookmarked');
    debugPrint('Station UID: ${_station!.uid}');

    // Add haptic feedback for better user experience
    HapticFeedback.mediumImpact();

    // Optimistic UI update - toggle immediately for better UX
    final newBookmarkStatus = !_isBookmarked;
    setState(() {
      _isBookmarked = newBookmarkStatus;
      _isBookmarkLoading = true;
    });
    debugPrint('✅ Optimistic UI update applied: $newBookmarkStatus');

    try {
      // Make API call to update bookmark status
      final response = await _apiService.post(
        ApiConfig.saveBookmark,
        data: {
          'location_uid': _station!.uid!,
          'status': newBookmarkStatus ? 1 : 0, // 1 for bookmark, 0 for unbookmark
        },
      );

      debugPrint('📡 Bookmark API response: $response');
      debugPrint('📡 Response type: ${response.runtimeType}');
      debugPrint('📡 Response keys: ${response.keys.toList()}');
      debugPrint('📡 Success value: ${response['success']} (${response['success'].runtimeType})');

      // Check for successful response
      if (response['success'] == true) {
        debugPrint('✅ Bookmark updated successfully: ${response['message']}');

        // Show success feedback
        _showBookmarkSnackbar(
          newBookmarkStatus ? 'Station bookmarked' : 'Bookmark removed',
          isSuccess: true,
        );

        // Sync with server to ensure consistency
        await Future.delayed(const Duration(milliseconds: 300));
        await _fetchBookmarkStatus();

      } else {
        // API call failed - revert to previous state
        final errorMessage = response['message'] ?? 'Failed to update bookmark';
        debugPrint('❌ Bookmark update failed: $errorMessage');

        // Revert the optimistic update
        setState(() {
          _isBookmarked = !newBookmarkStatus;
        });
        debugPrint('🔄 Reverted to previous state: ${!newBookmarkStatus}');

        // Show error message to user
        _showBookmarkSnackbar(errorMessage, isSuccess: false);
      }

    } catch (error) {
      debugPrint('❌ Bookmark API error: $error');

      // Revert the optimistic update on error
      setState(() {
        _isBookmarked = !newBookmarkStatus;
      });
      debugPrint('🔄 Reverted to previous state due to error: ${!newBookmarkStatus}');

      // Show error message to user
      String errorMessage = 'Failed to update bookmark';
      if (error.toString().contains('timeout')) {
        errorMessage = 'Connection timeout. Please check your internet connection.';
      } else if (error.toString().contains('401') || error.toString().contains('403')) {
        errorMessage = 'Authentication error. Please log in again.';
      } else if (error.toString().contains('500')) {
        errorMessage = 'Server error. Please try again later.';
      }
      _showBookmarkSnackbar(errorMessage, isSuccess: false);
    } finally {
      // Clear loading state
      if (mounted) {
        setState(() {
          _isBookmarkLoading = false;
        });
      }
    }
  }

  /// Fetch current bookmark status from server
  Future<void> _fetchBookmarkStatus() async {
    if (_station?.uid == null) return;

    try {
      debugPrint('📡 Fetching bookmark status for station: ${_station!.uid}');

      final response = await _apiService.get(ApiConfig.getBookmarks);

      if (response['success'] == true && response['data'] != null) {
        final bookmarks = response['data'] as List<dynamic>;

        // Check if current station is in bookmarks list
        final isBookmarked = bookmarks.any((bookmark) =>
          bookmark['location_uid'] == _station!.uid &&
          bookmark['status'] == 1
        );

        if (mounted) {
          setState(() {
            _isBookmarked = isBookmarked;
          });
        }

        debugPrint('✅ Bookmark status fetched: $isBookmarked');
      }
    } catch (error) {
      debugPrint('❌ Error fetching bookmark status: $error');
      // Don't show error to user for background fetch
    }
  }

  /// Show bookmark feedback snackbar
  void _showBookmarkSnackbar(String message, {required bool isSuccess}) {
    if (!mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(
              isSuccess ? Icons.check_circle : Icons.error,
              color: Colors.white,
              size: 20,
            ),
            const SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: isSuccess ? Colors.green : Colors.red,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  /// Share station details with location data and Google Maps link
  Future<void> _shareStationDetails() async {
    if (_station == null) return;

    try {
      // Add haptic feedback for better user experience
      HapticFeedback.lightImpact();

      final station = _station!;
      final stationName = station.name;
      final stationAddress = station.address;
      final latitude = station.latitude;
      final longitude = station.longitude;

      // Create Google Maps link for the station location
      final googleMapsUrl = 'https://www.google.com/maps/search/?api=1&query=$latitude,$longitude';

      // Create comprehensive share text with station details
      final shareText = '''🔋 EV Charging Station

📍 $stationName
📍 $stationAddress

🗺️ Location: $latitude, $longitude

🔗 Open in Google Maps: $googleMapsUrl

⚡ Find this charging station and more on EcoPlug!

#EVCharging #EcoPlug #ElectricVehicle #GreenTransport''';

      // Share the station details
      await Share.share(
        shareText,
        subject: 'EV Charging Station - $stationName',
      );

      debugPrint('✅ Station details shared successfully');

      // Show success feedback
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Row(
              children: [
                Icon(Icons.share, color: Colors.white, size: 20),
                SizedBox(width: 8),
                Text('Station details shared successfully!'),
              ],
            ),
            backgroundColor: Colors.green,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.all(Radius.circular(12)),
            ),
            duration: Duration(seconds: 2),
          ),
        );
      }

    } catch (error) {
      debugPrint('❌ Error sharing station details: $error');

      // Show error feedback
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error, color: Colors.white, size: 20),
                const SizedBox(width: 8),
                Expanded(child: Text('Failed to share: ${error.toString()}')),
              ],
            ),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  // REMOVED: _updateLoadingState method - PERMANENTLY ELIMINATED TO PREVENT LOADING ISSUES

  // Build error screen for critical data validation failures
  Widget _buildErrorScreen(String message) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final primaryColor = Theme.of(context).colorScheme.primary;

    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: Padding(
          padding: const EdgeInsets.all(8.0),
          child: CircleAvatar(
            backgroundColor: Colors.white.withAlpha(204),
            child: IconButton(
              icon: const Icon(Icons.arrow_back, color: Colors.black87),
              onPressed: () => Navigator.pop(context),
            ),
          ),
        ),
      ),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Error icon
              Container(
                width: 100,
                height: 100,
                decoration: BoxDecoration(
                  color: Colors.red.withAlpha(25),
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.error_outline_rounded,
                  size: 60,
                  color: Colors.red,
                ),
              ),
              const SizedBox(height: 32),
              // Error title
              Text(
                'Incomplete Station Data',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: isDarkMode ? Colors.white : Colors.black87,
                    ),
              ),
              const SizedBox(height: 16),
              // Error message
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: isDarkMode
                      ? Colors.grey.shade800.withAlpha(128)
                      : Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: isDarkMode
                        ? Colors.grey.shade700
                        : Colors.grey.shade300,
                    width: 1,
                  ),
                ),
                child: SelectableText(
                  message,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: isDarkMode
                            ? Colors.grey.shade300
                            : Colors.grey.shade800,
                      ),
                  textAlign: TextAlign.center,
                ),
              ),
              const SizedBox(height: 32),
              // Go back button
              ElevatedButton.icon(
                onPressed: () => Navigator.pop(context),
                icon: const Icon(Icons.arrow_back),
                label: const Text('Go Back'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: primaryColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 12,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(30),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    _stopBackgroundRefresh();
    _stopGunConnectionMonitoring(); // Stop gun connection monitoring
    _tabController.dispose();
    _snackbarTimer?.cancel(); // Clean up timer
    super.dispose();
  }

  /// Start background refresh mechanism (simplified)
  void _startBackgroundRefresh() {
    if (_isBackgroundRefreshActive) return;

    _isBackgroundRefreshActive = true;
    _scheduleNextRefresh();
  }

  /// Stop background refresh and clean up resources
  void _stopBackgroundRefresh() {
    _isBackgroundRefreshActive = false;
    _backgroundRefreshTimer?.cancel();
    _backgroundRefreshTimer = null;
  }

  /// 🚨 NEW: Stop background refresh when charging flow begins
  /// This prevents race conditions between background refresh and charging APIs
  void _stopBackgroundRefreshForCharging() {
    debugPrint('🛡️ STOPPING background refresh for charging flow');
    _isChargingFlowActive = true;
    _stopBackgroundRefresh();
    debugPrint(
        '✅ Background refresh stopped - charging flow can proceed safely');
  }

  /// 🚨 NEW: Resume background refresh when charging flow ends
  /// This allows background refresh to continue after charging is complete
  void _resumeBackgroundRefreshAfterCharging() {
    debugPrint('🔄 RESUMING background refresh after charging flow');
    _isChargingFlowActive = false;
    if (mounted && _station != null) {
      _startBackgroundRefresh();
      debugPrint('✅ Background refresh resumed');
    }
  }

  /// Schedule next background refresh
  void _scheduleNextRefresh() {
    if (!_isBackgroundRefreshActive || !mounted) return;

    _backgroundRefreshTimer?.cancel();
    // 🚨 UPDATED: Changed to 5 seconds for real-time connector status updates
    // This provides timely updates while maintaining good performance
    _backgroundRefreshTimer = Timer(const Duration(seconds: 5), () {
      _performBackgroundRefresh();
    });
  }

  /// Perform background refresh (simplified)
  Future<void> _performBackgroundRefresh() async {
    if (!_isBackgroundRefreshActive || !mounted) return;

    // 🚨 RACE CONDITION PREVENTION: Stop background refresh if charging flow is active
    if (_isChargingFlowActive) {
      debugPrint('🛡️ Background refresh skipped - charging flow is active');
      return;
    }

    try {
      // 🌐 CHECK CONNECTIVITY: Verify internet connection before background refresh
      final connectivityService = ConnectivityService();
      final hasConnection = await connectivityService.checkConnectionManually();

      if (!hasConnection) {
        debugPrint('🌐 BACKGROUND REFRESH: No internet connection - skipping background refresh');
        return; // Skip background refresh silently when offline
      }

      // Fetch station details silently in background
      final response = await _apiBridge.getStationDetailsByUid(widget.uid);

      if (response != null && mounted) {
        // Compare with last known data to detect changes
        if (_hasConnectorStatusChanged(response)) {
          _updateConnectorStatusOnly(response);
        }

        // Store as last known data
        _lastKnownData = response;
      }
    } catch (e) {
      // Silently handle errors in background refresh
    }

    // Schedule next refresh regardless of success/failure
    _scheduleNextRefresh();
  }

  /// Check if connector status has changed
  bool _hasConnectorStatusChanged(
      station_details.StationDetailsResponse newData) {
    if (_lastKnownData == null || newData.data?.evses == null) return true;

    final oldEvses = _lastKnownData!.data?.evses ?? {};
    final newEvses = newData.data!.evses!;

    // Compare connector statuses
    for (final newEvseEntry in newEvses.entries) {
      final newEvse = newEvseEntry.value;
      final oldEvse = oldEvses[newEvseEntry.key];

      if (oldEvse == null) return true; // New EVSE detected

      for (final newConnector in newEvse.connectors ?? []) {
        final oldConnector = oldEvse.connectors?.firstWhere(
          (c) => c.connectorId == newConnector.connectorId,
          orElse: () => station_details.Connector(),
        );

        if (oldConnector?.status != newConnector.status) {
          return true; // Status changed
        }
      }
    }

    return false; // No changes detected
  }

  /// Update only connector status without full re-render
  void _updateConnectorStatusOnly(
      station_details.StationDetailsResponse newData) {
    if (!mounted || newData.data?.evses == null) return;

    // Extract updated connectors and update EVSE power output mapping
    List<station_details.Connector> updatedConnectors = [];
    for (final evseEntry in newData.data!.evses!.entries) {
      final evse = evseEntry.value;
      final evsesUid = evseEntry.key;

      // Update EVSE power output mapping for power type display
      if (evse.powerOutput != null && evse.powerOutput!.isNotEmpty) {
        _evsePowerOutputMap[evsesUid] = evse.powerOutput!;
      }

      if (evse.connectors != null) {
        // CRITICAL FIX: DO NOT overwrite connector's evses_uid with EVSE key
        // The connector already has the correct evses_uid from API response
        for (var connector in evse.connectors!) {
          // Preserve connector's original evses_uid - DO NOT overwrite with EVSE key
          if (connector.evsesUid == null || connector.evsesUid!.isEmpty) {
            // Only as fallback if missing (should not happen)
            connector.evsesUid = evsesUid;
          }
          // connector.evsesUid should remain as "e409811c-a6b1-4b04-92e6-3a219a824bfc"
        }
        updatedConnectors.addAll(evse.connectors!);
      }
    }

    // Update cache
    _connectorsCache[widget.uid] = updatedConnectors;

    // CRITICAL FIX: Preserve gun selection state during status updates
    station_details.Connector? preservedSelectedConnector;
    if (_selectedConnector != null) {
      // Find the updated version of the currently selected connector
      preservedSelectedConnector = updatedConnectors.firstWhere(
        (c) => c.connectorId == _selectedConnector!.connectorId,
        orElse: () => _selectedConnector!,
      );

      // Check if the selected connector should remain selected based on new status
      final newStatus = preservedSelectedConnector.status?.toLowerCase() ?? '';
      final shouldRemainSelected = _shouldPreserveSelection(newStatus);

      debugPrint('🔧 Gun Selection Preservation:');
      debugPrint('   Selected Connector: ${_selectedConnector!.connectorId}');
      debugPrint('   Old Status: ${_selectedConnector!.status}');
      debugPrint('   New Status: ${preservedSelectedConnector.status}');
      debugPrint('   Should Remain Selected: $shouldRemainSelected');

      if (!shouldRemainSelected) {
        debugPrint('🔧 Deselecting gun due to status change to unavailable state');
        _selectedConnector = null;
        _showRefreshSnackbar('Gun deselected due to status change', isError: true);
      } else {
        debugPrint('🔧 Preserving gun selection - status allows continued selection');
        _selectedConnector = preservedSelectedConnector;
      }
    }

    // Update station object with new connector data only
    if (_station != null) {
      setState(() {
        // Convert station details connectors to station model connectors
        debugPrint('🔄 BACKGROUND UPDATE: Converting ${updatedConnectors.length} connectors');
        final updatedStationConnectors = updatedConnectors
            .map((c) {
              debugPrint('🔄 Converting connector ${c.connectorId} with status: "${c.status}"');
              return Connector(
                id: c.connectorId ?? '',
                name: c.label ?? c.type ?? 'Connector',
                type: c.type ?? '',
                price: c.pricePerUnit?.toDouble() ?? 0.0,
                power: c.maxElectricPower?.toString() ?? '0',
                totalGuns: 1,
                availableGuns:
                    (c.status?.toLowerCase() == 'available') ? 1 : 0,
                icon: c.icon,
                status: c.status, // Preserve exact API status
                maxElectricPower: c.maxElectricPower,
                standard: c.standard,
                priceLabel: c.priceLabel,
                pricePerUnit: c.pricePerUnit,
                soc: c.soc,
                evsesUid: c.evsesUid,
                iconUrl: c.icon,
              );
            })
            .toList();

        debugPrint('✅ BACKGROUND UPDATE: Converted connectors with statuses:');
        for (final conn in updatedStationConnectors) {
          debugPrint('   - ${conn.id}: "${conn.status}"');
        }

        // Create new station with updated connectors
        _station = Station(
          id: _station!.id,
          name: _station!.name,
          address: _station!.address,
          city: _station!.city,
          state: _station!.state,
          images: _station!.images,
          evses: _station!.evses,
          latitude: _station!.latitude,
          longitude: _station!.longitude,
          distance: _station!.distance,
          status: _station!.status,
          rating: _station!.rating,
          reviews: _station!.reviews,
          connectors: updatedStationConnectors,
          mapPinUrl: _station!.mapPinUrl,
          focusedMapPinUrl: _station!.focusedMapPinUrl,
          types: _station!.types,
          uid: _station!.uid,
          openingTimes: _station!.openingTimes,
          openStatus: _station!.openStatus,
        );
      });
    }
  }

  /// Show snackbar for busy/offline connectors with auto-dismiss and overlap prevention
  void _showConnectorStatusSnackbar(String message) {
    // Prevent snackbar overlap/spam
    if (_isSnackbarVisible) {
      return; // Don't show new snackbar if one is already visible
    }

    _isSnackbarVisible = true;

    // Cancel any existing timer
    _snackbarTimer?.cancel();

    // Show the snackbar
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        duration: const Duration(seconds: 1), // Set duration to 1 second
      ),
    );

    // Set timer to reset the flag after 1 second
    _snackbarTimer = Timer(const Duration(seconds: 1), () {
      _isSnackbarVisible = false;
    });
  }

  /// Start monitoring gun connection for all connectors
  void _startGunConnectionMonitoring() {
    // CRITICAL SAFETY: Temporarily disable gun connection monitoring to prevent API conflicts
    // This ensures station details page loads without interference
    debugPrint(
        '🛡️ SAFETY: Gun connection monitoring is temporarily disabled to prevent API conflicts');
    debugPrint(
        '🛡️ Station details page will load without gun connection monitoring');
    return;

    // Original gun connection monitoring code (disabled for safety)
    /*
    if (_station?.uid == null) return;

    final connectors = _connectorsCache[_station!.uid] ?? [];

    for (final connector in connectors) {
      if (connector.connectorId != null && connector.connectorId!.isNotEmpty) {
        // CRITICAL FIX: Use connector-specific evses_uid instead of main station UID
        final String correctEvseUidForCharging = connector.evsesUid ?? '';

        if (correctEvseUidForCharging.isEmpty) {
          debugPrint(
              '⚠️ GunConnectionService: Skipping connector ${connector.connectorId} in station ${_station?.uid} due to missing or empty connector.evsesUid');
          continue;
        }

        final connectorKey =
            '${correctEvseUidForCharging}_${connector.connectorId}';

        // Skip if already monitoring
        if (_gunConnectionSubscriptions.containsKey(connectorKey)) continue;

        // Start monitoring this connector with correct evses_uid
        debugPrint(
            '🔌 Starting gun connection monitoring for connector ${connector.connectorId} with evses_uid: $correctEvseUidForCharging');
        final subscription = _gunConnectionService
            .monitorGunConnection(
          evseUid:
              correctEvseUidForCharging, // FIXED: Use connector-specific evses_uid
          connectorId: connector.connectorId!,
        )
            .listen((isConnected) {
          if (mounted) {
            setState(() {
              _gunConnectionStatus[connectorKey] = isConnected;
            });
            debugPrint(
                '🔌 Gun connection status for ${connector.connectorId}: $isConnected');
          }
        });

        _gunConnectionSubscriptions[connectorKey] = subscription;
      }
    }
    */
  }

  /// Stop monitoring gun connections
  void _stopGunConnectionMonitoring() {
    for (final subscription in _gunConnectionSubscriptions.values) {
      subscription.cancel();
    }
    _gunConnectionSubscriptions.clear();
    _gunConnectionStatus.clear();
    _gunConnectionService.stopAllMonitoring();
  }

  /// Check if gun is connected for a specific connector
  bool _isGunConnected(station_details.Connector connector) {
    if (connector.connectorId == null || connector.evsesUid == null)
      return false;

    // FIXED: Use connector-specific evses_uid for consistent key format
    final connectorKey = '${connector.evsesUid}_${connector.connectorId}';
    return _gunConnectionStatus[connectorKey] ?? false;
  }

  /// Check if connector is selectable based on 5 specific API status values
  /// API Status Values: "Available", "Gun Connected", "Charging", "Unavailable", "Offline"
  /// Returns true ONLY for "Available" and "Gun Connected" statuses
  /// Returns false for "Charging", "Unavailable", and "Offline" statuses
  bool _isConnectorSelectable(String? status) {
    if (status == null || status.isEmpty) return false;

    final statusLower = status.toLowerCase().trim();

    // ✅ SELECTABLE: Allow selection for these 2 statuses only
    if (statusLower == 'available') {
      return true;
    }
    if (statusLower == 'gun connected') {
      return true;
    }

    // ❌ NOT SELECTABLE: Disable selection for these 3 statuses
    if (statusLower == 'charging') {
      return false;
    }
    if (statusLower == 'unavailable') {
      return false;
    }
    if (statusLower == 'offline') {
      return false;
    }

    // For any other status not in the 5 specific API values, default to NOT selectable
    // This ensures only the specified statuses are selectable
    return false;
  }

  /// Check if connector is currently charging
  bool _isConnectorCharging(String? status) {
    if (status == null || status.isEmpty) return false;
    return status.toLowerCase().trim() == 'charging';
  }

  /// Build decoration for special status connector cards (charging and gun connected)
  /// with background fill and prominent left edge design
  BoxDecoration _buildSpecialStatusDecoration(BuildContext context, String? status) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    // Determine color based on status
    Color statusColor;
    if (status?.toLowerCase().trim() == 'charging') {
      statusColor = _chargingLimeGreen; // Use lime green for charging
    } else if (status?.toLowerCase().trim() == 'gun connected') {
      statusColor = electricYellow;
    } else {
      // Fallback to lime green for any other special status
      statusColor = _chargingLimeGreen;
    }

    return BoxDecoration(
      // Status-based background fill (lime green for charging, yellow for gun connected)
      color: statusColor.withAlpha(isDarkMode ? 40 : 25),
      // Standard border radius for the card
      borderRadius: BorderRadius.circular(10),
      // Prominent left border with rounded corners matching card design
      border: Border(
        left: BorderSide(
          color: statusColor,
          width: 4.0, // Thick left edge indicator with rounded corners
        ),
        top: BorderSide(
          color: statusColor,
          width: 1.0, // Minimal top border
        ),
        right: BorderSide(
          color: statusColor,
          width: 1.0, // Minimal right border
        ),
        bottom: BorderSide(
          color: statusColor,
          width: 1.0, // Minimal bottom border
        ),
      ),
    );
  }

  /// Get appropriate status message for non-selectable connectors
  /// Provides specific messages for the 3 non-selectable API status values
  String _getConnectorStatusMessage(String? status) {
    if (status == null || status.isEmpty) {
      return 'This connector is currently unavailable';
    }

    final statusLower = status.toLowerCase().trim();

    // Specific messages for the 3 non-selectable API status values
    if (statusLower == 'charging') {
      return 'This connector is currently charging';
    }
    if (statusLower == 'unavailable') {
      return 'This connector is currently unavailable';
    }
    if (statusLower == 'offline') {
      return 'This connector is currently offline';
    }

    // Generic message for any other non-selectable status
    return 'This connector is currently unavailable';
  }

  /// Determine if gun selection should be preserved based on new status
  bool _shouldPreserveSelection(String newStatus) {
    // Preserve selection for these statuses (selectable states)
    if (newStatus == 'available' || newStatus == 'gun connected') {
      return true;
    }

    // Deselect for these statuses (non-selectable states)
    if (newStatus == 'charging' || newStatus == 'unavailable' || newStatus == 'offline') {
      return false;
    }

    // For unknown statuses, be conservative and deselect
    return false;
  }

  /// Get connector color based on status
  /// Returns yellow for "gun connected" status, blue for all other states
  Color _getConnectorColor(String? status) {
    if (status == null || status.isEmpty) {
      return _unifiedBadgeColor; // Default unified badge color
    }

    final statusLower = status.toLowerCase().trim();

    // Use yellow for "gun connected" status
    if (statusLower == 'gun connected') {
      return electricYellow; // Use app's yellow color
    }

    // Use unified badge color for all other statuses (available, charging, unavailable, offline, etc.)
    return _unifiedBadgeColor; // Unified badge color
  }

  /// Get badge color based on connector status with priority logic
  /// Priority: charging (lime green) > gun connected (yellow) > default (unified blue)
  Color _getBadgeColor(String? status) {
    if (status == null || status.isEmpty) {
      return _unifiedBadgeColor; // Default unified badge color
    }

    final statusLower = status.toLowerCase().trim();

    // Priority 1: If status is "charging" - use lime green
    if (statusLower == 'charging') {
      return _chargingLimeGreen;
    }

    // Priority 2: If status is "gun connected" - use yellow
    if (statusLower == 'gun connected') {
      return electricYellow;
    }

    // Priority 3: For all other statuses - use unified badge color
    return _unifiedBadgeColor;
  }

  /// Show refresh status snackbar with proper styling
  void _showRefreshSnackbar(String message, {bool isError = false}) {
    // Prevent snackbar overlap/spam
    if (_isSnackbarVisible) {
      return;
    }

    _isSnackbarVisible = true;

    // Cancel any existing timer
    _snackbarTimer?.cancel();

    // Show the snackbar with appropriate styling
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(
              isError ? Icons.warning_rounded : Icons.refresh_rounded,
              color: Colors.white,
              size: 20,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                message,
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
        backgroundColor: isError ? Colors.orange[700] : Colors.green[700],
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        duration: Duration(seconds: isError ? 3 : 2),
        margin: const EdgeInsets.all(16),
      ),
    );

    // Set timer to reset the flag
    _snackbarTimer = Timer(Duration(seconds: isError ? 3 : 2), () {
      _isSnackbarVisible = false;
    });
  }

  @override
  void initState() {
    super.initState();

    // Initialize tab controller
    _tabController = TabController(length: 2, vsync: this);
    _tabController.addListener(() {
      if (mounted) {
        setState(() {
          _selectedTabIndex = _tabController.index;
        });
      }
    });

    print('✅ StationDetailsPage opened directly with UID: "${widget.uid}"');

    // Initialize _station with passed station or a placeholder for loading
    if (widget.station != null) {
      _station = widget.station;
      print('✅ Initialized with passed station data: ${_station!.name}');
    } else {
      // Create a placeholder station object for the loading screen
      _station = Station(
        id: widget.uid,
        uid: widget.uid,
        name: 'Fetching Station Info...', // User-friendly loading name
        address: 'Loading address...',
        city: '',
        state: '',
        latitude: 0.0,
        longitude: 0.0,
        distance: 0.0, // Will be updated if location services are used
        status: 'Loading',
        rating: 0.0,
        reviews: 0,
        connectors: [],
        images: [
          'https://api2.eeil.online/uploads/ev-banner2.png'
        ], // Default placeholder image
        evses: [],
        openingTimes: null,
        openStatus: null,
      );
      print('✅ Created placeholder station for UID: ${widget.uid}');
    }

    _loadInitialData();
  }

  Future<void> _loadInitialData() async {
    // _isLoading is true by default
    try {
      if (mounted) {
        // Clear previous error if any at the start of a new load attempt
        setState(() {
          _errorMessage = null;
        });
      }
      await _fetchStationDetailsDirectly();
      await _fetchReviewsDirectly();

      // After fetching, if _station is still null and no error was set by _fetchStationDetailsDirectly,
      // it means data loading failed silently or didn't populate _station.
      if (_station == null && _errorMessage == null && mounted) {
        _updateErrorState("Failed to load station data. Please try again.");
      }
    } catch (e) {
      // This catch block is for unhandled exceptions from fetch methods
      // or other issues within the try block.
      if (mounted) {
        _updateErrorState(
            'An unexpected error occurred while loading data: $e');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// Fetch station details directly without loading states
  Future<void> _fetchStationDetailsDirectly() async {
    try {
      print('🔄 Fetching station details directly for UID: ${widget.uid}');

      // 🌐 CHECK CONNECTIVITY: Verify internet connection before making API call
      final connectivityService = ConnectivityService();
      final hasConnection = await connectivityService.checkConnectionManually();

      if (!hasConnection) {
        debugPrint('🌐 NO INTERNET: Cannot fetch station details');
        if (mounted) {
          // Show the connectivity error page using the existing service
          await ConnectivityErrorService.showConnectivityError(
            context,
            customMessage: 'No internet connection. Please check your network settings and try again.',
            onRetry: () {
              Navigator.of(context).pop();
              _fetchStationDetailsDirectly(); // Retry the fetch
            },
          );
        }
        return;
      }

      final stationDetails =
          await _apiBridge.getStationDetailsByUid(widget.uid);

      if (stationDetails?.data != null && mounted) {
        // Use the request UID if API response UID is null/empty
        final finalUid = stationDetails!.data!.uid?.isNotEmpty == true
            ? stationDetails.data!.uid!
            : widget.uid;

        // Extract connectors from EVSEs and store in cache
        List<Connector> appConnectors = [];
        List<station_details.Connector> apiConnectors = [];

        if (stationDetails.data!.evses != null) {
          debugPrint('🔍 CONNECTOR STATUS DEBUG: Processing ${stationDetails.data!.evses!.length} EVSEs');

          for (final evseEntry in stationDetails.data!.evses!.entries) {
            final evse = evseEntry.value;
            final evseUid = evseEntry.key;
            debugPrint('🔍 Processing EVSE: $evseUid');

            if (evse.connectors != null) {
              debugPrint('🔍 EVSE $evseUid has ${evse.connectors!.length} connectors');

              for (final connector in evse.connectors!) {
                debugPrint('🔍 CONNECTOR RAW DATA:');
                debugPrint('   - ID: ${connector.connectorId}');
                debugPrint('   - Type: ${connector.type}');
                debugPrint('   - Status: "${connector.status}" (${connector.status.runtimeType})');
                debugPrint('   - Label: ${connector.label}');
                debugPrint('   - EVSE UID: ${connector.evsesUid}');
                debugPrint('   - Max Power: ${connector.maxElectricPower}');

                if (connector.type?.isNotEmpty == true) {
                  // Store the API connector for cache
                  apiConnectors.add(connector);

                  // Create app connector with preserved status
                  final preservedStatus = connector.status ?? 'Unknown';
                  debugPrint('🔍 CREATING APP CONNECTOR with status: "$preservedStatus"');

                  appConnectors.add(Connector(
                    id: connector.connectorId ??
                        '${finalUid}_${connector.type}',
                    name:
                        connector.label ?? 'Connector ${connector.connectorId}',
                    type: connector.type!,
                    status: preservedStatus, // Preserve exact API status
                    power: connector.maxElectricPower?.toString() ?? '',
                    maxElectricPower: connector.maxElectricPower,
                    price: connector.price?.toDouble() ?? 0.0,
                    priceLabel: connector.priceLabel ?? '',
                    icon: connector.icon ?? '',
                    availableGuns: preservedStatus.toLowerCase() == 'available' ? 1 : 0,
                    evsesUid: connector.evsesUid, // Preserve EVSE UID
                  ));

                  debugPrint('✅ App connector created with status: "$preservedStatus"');
                } else {
                  debugPrint('⚠️ Skipping connector with empty type');
                }
              }
            } else {
              debugPrint('⚠️ EVSE $evseUid has no connectors');
            }
          }
        }

        setState(() {
          _station = Station(
            id: finalUid,
            uid: finalUid,
            name: stationDetails.data!.name ?? 'Unknown Station',
            address: stationDetails.data!.address ?? 'Address not available',
            city: stationDetails.data!.city ?? '',
            state: stationDetails.data!.state ?? '',
            latitude: stationDetails.data!.latitude ?? 0.0,
            longitude: stationDetails.data!.longitude ?? 0.0,
            distance: 0.0,
            status: 'Available',
            rating: stationDetails.data!.rate ?? 0.0,
            reviews: stationDetails.data!.rateTotal ?? 0,
            connectors: appConnectors,
            images: stationDetails.data!.images?.isNotEmpty == true
                ? [stationDetails.data!.images!]
                : ['https://api2.eeil.online/uploads/ev-banner2.png'],
            evses: [],
            openingTimes: stationDetails.data!.openingTimes,
            openStatus: stationDetails.data!.openStatus,
          );

          // Store connectors in cache for gun connection monitoring
          _connectorsCache[finalUid] = apiConnectors;

          // Store raw station data
          _rawStationData = stationDetails.data;

          // Store wallet and instant charging data
          _walletData = stationDetails.wallet;
          _instantCharging = stationDetails.instantCharging;
        });
        print('✅ Station details updated: ${_station!.name}');
        print('✅ Found ${appConnectors.length} connectors');

        // 🚨 CRITICAL FIX: Start background services after successful data load
        // This was missing and causing background refresh to never start
        _startBackgroundServicesAfterLoad();

        // Fetch bookmark status after station data is loaded
        _fetchBookmarkStatus();
      }
    } catch (e) {
      print('❌ Error fetching station details: $e');

      // 🌐 CHECK IF ERROR IS CONNECTIVITY-RELATED: Use existing service to handle properly
      if (ConnectivityErrorService.isConnectivityError(e)) {
        debugPrint('🌐 CONNECTIVITY ERROR detected in station details fetch');
        if (mounted) {
          // Show connectivity error page using existing service
          await ConnectivityErrorService.showConnectivityError(
            context,
            customMessage: ConnectivityErrorService.getConnectivityErrorMessage(e),
            onRetry: () {
              Navigator.of(context).pop();
              _fetchStationDetailsDirectly(); // Retry the fetch
            },
          );
        }
      } else {
        // Handle non-connectivity errors normally
        if (mounted) {
          _updateErrorState('Failed to fetch station details: $e');
        }
      }
    }
  }

  /// Fetch reviews directly without loading states
  Future<void> _fetchReviewsDirectly() async {
    try {
      print('🔄 Fetching reviews directly for UID: ${widget.uid}');
      // Use the existing _fetchReviews method but ignore loading states
      await _fetchReviews();
      print('✅ Reviews fetch completed');
    } catch (e) {
      print('❌ Error fetching reviews: $e');
    }
  }

  /// Stop all background services to prevent interference during initialization
  void _stopAllBackgroundServices() {
    debugPrint(
        '🛡️ CRITICAL: Stopping all background services to prevent interference');

    // Stop background refresh
    _stopBackgroundRefresh();

    // Stop gun connection monitoring
    _stopGunConnectionMonitoring();

    // Clear any existing timers
    _snackbarTimer?.cancel();

    debugPrint('✅ All background services stopped successfully');
  }

  /// Initialize data with comprehensive error handling and safety mechanisms
  Future<void> _initializeDataWithFullErrorHandling() async {
    if (!mounted) return;

    debugPrint('🚀 Starting comprehensive station details initialization');

    try {
      // REMOVED: Loading state logic - PERMANENTLY ELIMINATED
      if (mounted) {
        setState(() {
          _errorMessage = '';
        });
      }

      // Add overall timeout for initialization process with multiple safety layers
      await Future.wait([
        _fetchStationDetailsWithSafety(),
        _fetchReviewsWithSafety(),
      ]).timeout(const Duration(seconds: 45)); // Increased timeout for safety

      debugPrint('✅ Station details initialization completed successfully');
    } catch (e) {
      debugPrint('❌ Station details initialization failed: $e');
      if (mounted) {
        setState(() {
          if (e is TimeoutException) {
            _errorMessage =
                'Loading timed out. Please check your internet connection and try again.';
          } else {
            _errorMessage = 'Failed to initialize data: $e';
          }
          // REMOVED: _isLoading = false; - PERMANENTLY ELIMINATED
          _isLoadingReviews = false;
        });
      }
    } finally {
      // REMOVED: Loading state safety logic - PERMANENTLY ELIMINATED
      debugPrint(
          '✅ Initialization completed (loading state permanently removed)');
    }
  }

  /// Fetch station details with additional safety mechanisms
  Future<void> _fetchStationDetailsWithSafety() async {
    try {
      await _fetchStationDetailsDirectly();
    } catch (e) {
      debugPrint('❌ _fetchStationDetailsWithSafety error: $e');
      // Re-throw to be handled by parent
      rethrow;
    }
  }

  /// Fetch reviews with additional safety mechanisms
  Future<void> _fetchReviewsWithSafety() async {
    try {
      await _fetchReviews();
    } catch (e) {
      debugPrint('❌ _fetchReviewsWithSafety error: $e');
      // Don't re-throw for reviews as they're not critical for page display
      if (mounted) {
        setState(() {
          _isLoadingReviews = false;
          _reviews = [];
        });
      }
    }
  }

  /// Start background services only after successful page load
  void _startBackgroundServicesAfterLoad() {
    debugPrint('🚀 Starting background services after successful page load');

    // Add a small delay to ensure page is fully rendered
    Future.delayed(const Duration(milliseconds: 500), () {
      if (mounted && _station != null) {
        // Start background refresh
        _startBackgroundRefresh();

        // CRITICAL: Only start gun connection monitoring if we have valid connectors
        final connectors = _connectorsCache[_station!.uid] ?? [];
        if (connectors.isNotEmpty) {
          debugPrint(
              '✅ Starting gun connection monitoring for ${connectors.length} connectors');
          _startGunConnectionMonitoringDelayed();
        } else {
          debugPrint(
              '⚠️ Skipping gun connection monitoring - no connectors available');
        }
      }
    });
  }

  /// Start gun connection monitoring with additional delay and safety checks
  void _startGunConnectionMonitoringDelayed() {
    // Add additional delay to prevent interference
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted && _station != null) {
        _startGunConnectionMonitoring();
      }
    });
  }

  // Static cache for connector icons to avoid repeated network requests across instances
  static final Map<String, Widget> _connectorIconCache = {};

  // Fetch reviews for the station
  Future<void> _fetchReviews() async {
    // The location_id for reviews is the same as the UID used to fetch station details
    final String locationId = widget.uid;

    if (locationId.isEmpty) {
      debugPrint('Cannot fetch reviews: Location ID (UID) is empty');
      setState(() {
        _isLoadingReviews = false;
      });
      return;
    }

    // Only set loading state if it's not already set
    // This prevents showing duplicate loading indicators
    if (!_isLoadingReviews) {
      setState(() {
        _isLoadingReviews = true;
      });
    }

    try {
      // 🌐 CHECK CONNECTIVITY: Verify internet connection before making API call
      final connectivityService = ConnectivityService();
      final hasConnection = await connectivityService.checkConnectionManually();

      if (!hasConnection) {
        debugPrint('🌐 NO INTERNET: Cannot fetch reviews');
        if (mounted) {
          setState(() {
            _isLoadingReviews = false;
            _reviews = [];
          });
        }
        return; // Skip review fetch silently when offline
      }

      debugPrint('Fetching reviews for location ID: $locationId');

      // Make a direct HTTP request to the reviews API
      final url =
          'https://api2.eeil.online/api/v1/user/reviews?location_id=$locationId';

      // Get the auth token using SharedPreferences directly for reliability
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');

      debugPrint(
          'Auth token from SharedPreferences: ${token != null ? "Found" : "Not found"}');

      if (token == null || token.isEmpty) {
        debugPrint('No auth token available for reviews API request');
        // Try to get token from TokenService as fallback
        final tokenService = TokenService();
        final fallbackToken = await tokenService.getToken();

        if (fallbackToken == null || fallbackToken.isEmpty) {
          debugPrint('No auth token available from TokenService either');
          setState(() {
            _isLoadingReviews = false;
            _reviews = [];
          });
          return;
        }

        debugPrint('Using fallback token from TokenService');
      }

      // Create headers with auth token
      final headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': 'Bearer ${token ?? ""}',
      };

      debugPrint('Making request to: $url');
      // Make the request
      final response = await http.get(Uri.parse(url), headers: headers);
      debugPrint('Response status code: ${response.statusCode}');

      if (mounted) {
        setState(() {
          if (response.statusCode == 200) {
            final jsonData = jsonDecode(response.body);
            debugPrint('Response body: ${response.body}');

            if (jsonData['success'] == true) {
              // Parse reviews from the response
              final reviewsList = jsonData['reviews'] as List<dynamic>? ?? [];

              // Convert to a list of maps
              _reviews = reviewsList.map((review) {
                return review as Map<String, dynamic>;
              }).toList();

              debugPrint('Successfully fetched ${_reviews.length} reviews');
            } else {
              debugPrint(
                  'Failed to fetch reviews: ${jsonData['message'] ?? 'Unknown error'}');
              _reviews = [];
            }
          } else {
            debugPrint(
                'Error fetching reviews: ${response.statusCode} - ${response.body}');
            _reviews = [];
          }

          // Only update the reviews loading state, not the main loading state
          _isLoadingReviews = false;
        });
      }
    } catch (e) {
      debugPrint('Error fetching reviews: $e');
      if (mounted) {
        setState(() {
          _isLoadingReviews = false;
          _reviews = [];
        });
      }
    }
  }

  // REMOVED: Old _fetchStationDetails method - PERMANENTLY ELIMINATED TO PREVENT LOADING ISSUES
  // This method was causing infinite loading loops and has been replaced with _fetchStationDetailsDirectly()

  // This is a duplicate dispose method, removing it

  // We only use real-time data from the API with no fallbacks

  //
// SECTION 1: STATION INFORMATION
//
  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Scaffold(
        appBar: AppBar(
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () => Navigator.pop(context),
          ),
          elevation: 0, // Consistent with error screen and main screen
          backgroundColor: Colors.transparent, // Consistent
        ),
        extendBodyBehindAppBar: true, // Consistent
        body: StationLoadingAnimation(
          stationName: _station?.name ??
              widget.uid, // Use station name if available, else UID
          errorMessage: _errorMessage, // Pass error message from state
          loadingMessage: 'Fetching station details, please wait...',
          onRetry: _errorMessage != null && _errorMessage!.isNotEmpty
              ? _loadInitialData
              : null, // Provide retry only if there's an error
        ),
      );
    }

    if (_errorMessage != null && _errorMessage!.isNotEmpty) {
      return _buildErrorScreen(_errorMessage!);
    }

    if (_station == null) {
      return _buildErrorScreen(
          'Station data could not be loaded. Please try again.');
    }

    // BULLETPROOF: _station is GUARANTEED to never be null due to initState initialization
    // NO LOADING CHECKS NEEDED - Page displays immediately with data

    // BULLETPROOF: No validation checks that could block page display
    // Page will ALWAYS display, even with placeholder data
    // Real data will be loaded in background and update the UI

    // BULLETPROOF: No error UI - page ALWAYS displays station content

    // Extract station details for easier access
    // At this point, _station is confirmed to be non-null.
    final stationName = _station!.name;
    final address = _station!.address;

    // CRITICAL: Only use real open status from API - no fallbacks
    final openStatus = _station!.openStatus != null
        ? _station!.openStatus!
            ? 'Open Now'
            : 'Closed'
        : 'Status not available'; // Show accurate message when API doesn't provide open status

    // CRITICAL: Use real opening times from API - no defaults
    String operatingHours = _station!.openingTimes ?? 'Hours not specified';

    return Scaffold(
      extendBodyBehindAppBar: true,
      // CRITICAL FIX: Only show bottom bar if station has connectors
      bottomNavigationBar: _station!.connectors.isNotEmpty
          ? _buildSelectConnectorBar(context, _selectedConnector)
          : null,
      body: RefreshIndicator(
        onRefresh: () async {
          // Show refresh start feedback
          _showRefreshSnackbar('Refreshing station data...');

          try {
            // Force refresh of station data
            await _fetchStationDetailsDirectly();

            // Show success feedback
            _showRefreshSnackbar('Station data refreshed successfully');
          } catch (e) {
            // Show error feedback
            _showRefreshSnackbar('Failed to refresh: $e', isError: true);
            rethrow; // Let RefreshIndicator handle the error state
          }
        },
        child: CustomScrollView(
          slivers: [
            // Enhanced banner with SliverAppBar for parallax effect
            SliverAppBar(
              expandedHeight: 220,
              floating: false,
              pinned: true,
              backgroundColor: Colors.transparent,
              elevation: 0,
              leading: Padding(
                padding: const EdgeInsets.all(8.0),
                child: CircleAvatar(
                  backgroundColor: Colors.white.withAlpha(204),
                  child: IconButton(
                    icon: const Icon(Icons.arrow_back, color: Colors.black87),
                    onPressed: () => Navigator.pop(context),
                  ),
                ),
              ),
              actions: [
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: CircleAvatar(
                    backgroundColor: Colors.white.withAlpha(204),
                    child: _isBookmarkLoading
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(Colors.black87),
                            ),
                          )
                        : IconButton(
                            icon: Icon(
                              _isBookmarked ? Icons.bookmark : Icons.bookmark_border,
                              color: _isBookmarked ? Colors.amber : Colors.black87,
                            ),
                            onPressed: _toggleBookmark,
                            tooltip: _isBookmarked ? 'Remove bookmark' : 'Add bookmark',
                          ),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: CircleAvatar(
                    backgroundColor: Colors.white.withAlpha(204),
                    child: IconButton(
                      icon: const Icon(Icons.share, color: Colors.black87),
                      onPressed: _shareStationDetails,
                    ),
                  ),
                ),
              ],
              flexibleSpace: FlexibleSpaceBar(
                background: Stack(
                  fit: StackFit.expand,
                  children: [
                    // Use station image from API
                    _station!.images.isNotEmpty &&
                            _station!.images[0].contains('http')
                        ? Image.network(
                            _station!.images[0],
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) {
                              return Container(
                                color: Colors.grey.shade300,
                                child: Center(
                                  child: Icon(
                                    Icons.image_not_supported,
                                    color: Colors.grey.shade500,
                                    size: 48,
                                  ),
                                ),
                              );
                            },
                            loadingBuilder: (context, child, loadingProgress) {
                              if (loadingProgress == null) return child;
                              return Container(
                                color: Colors.grey.shade200,
                                child: Center(
                                  child: CircularProgressIndicator(
                                    value: loadingProgress.expectedTotalBytes !=
                                            null
                                        ? loadingProgress
                                                .cumulativeBytesLoaded /
                                            loadingProgress.expectedTotalBytes!
                                        : null,
                                    strokeWidth: 2,
                                    color: const Color(0xFF43A047),
                                  ),
                                ),
                              );
                            },
                          )
                        : Container(
                            color: Colors.grey.shade300,
                            child: Center(
                              child: Icon(
                                Icons.image_not_supported,
                                color: Colors.grey.shade500,
                                size: 48,
                              ),
                            ),
                          ),
                    // Gradient overlay for better text visibility
                    Container(
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [
                            Colors.transparent,
                            Colors.black.withAlpha(128),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),

            // Station info and content
            SliverToBoxAdapter(
              child: Column(
                children: [
                  // Enhanced station info card
                  _buildStationInfoCard(
                    stationName,
                    address,
                    openStatus,
                    operatingHours,
                  ),

                  // Modern tab design with improved animations based on the image
                  Container(
                    margin: const EdgeInsets.fromLTRB(0, 8, 0, 16),
                    child: Column(
                      children: [
                        // Tab buttons in a row with modern design
                        Row(
                          children: [
                            // CHARGER tab
                            Expanded(
                              child: InkWell(
                                onTap: () {
                                  // Add haptic feedback for better user experience
                                  HapticFeedback.lightImpact();
                                  _tabController.animateTo(0);
                                },
                                splashColor:
                                    AppThemes.primaryColor.withAlpha(50),
                                highlightColor:
                                    AppThemes.primaryColor.withAlpha(30),
                                child: SizedBox(
                                  height: 50,
                                  child: Stack(
                                    alignment: Alignment.center,
                                    children: [
                                      // Tab text
                                      Center(
                                        child: Text(
                                          'CHARGER',
                                          style: TextStyle(
                                            color: _selectedTabIndex == 0
                                                ? AppThemes.primaryColor
                                                : Colors.grey.shade500,
                                            fontWeight: FontWeight.w800,
                                            fontSize: 18,
                                            letterSpacing: 0.5,
                                          ),
                                        ),
                                      ),
                                      // Animated ripple effect on tap
                                      if (_selectedTabIndex == 0)
                                        TweenAnimationBuilder<double>(
                                          tween: Tween<double>(
                                              begin: 0.0, end: 1.0),
                                          duration:
                                              const Duration(milliseconds: 300),
                                          curve: Curves.easeOutCubic,
                                          builder: (context, value, child) {
                                            return Positioned(
                                              bottom: 0,
                                              left: 0,
                                              right: 0,
                                              child: Container(
                                                height: 3,
                                                decoration: BoxDecoration(
                                                  color:
                                                      AppThemes.primaryColor,
                                                  borderRadius:
                                                      BorderRadius.circular(
                                                          1.5),
                                                  boxShadow: [
                                                    BoxShadow(
                                                      color: AppThemes.primaryColor
                                                          .withAlpha(76),
                                                      blurRadius: 4,
                                                      offset:
                                                          const Offset(0, 1),
                                                    ),
                                                  ],
                                                ),
                                                width: MediaQuery.of(context)
                                                        .size
                                                        .width *
                                                    0.5 *
                                                    value,
                                                margin: EdgeInsets.symmetric(
                                                  horizontal:
                                                      MediaQuery.of(context)
                                                              .size
                                                              .width *
                                                          0.25 *
                                                          (1 - value),
                                                ),
                                              ),
                                            );
                                          },
                                        ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                            // REVIEWS tab
                            Expanded(
                              child: InkWell(
                                onTap: () {
                                  // Add haptic feedback for better user experience
                                  HapticFeedback.lightImpact();
                                  _tabController.animateTo(1);
                                },
                                splashColor:
                                    AppThemes.primaryColor.withAlpha(50),
                                highlightColor:
                                    AppThemes.primaryColor.withAlpha(30),
                                child: SizedBox(
                                  height: 50,
                                  child: Stack(
                                    alignment: Alignment.center,
                                    children: [
                                      // Tab text
                                      Center(
                                        child: Text(
                                          'REVIEWS',
                                          style: TextStyle(
                                            color: _selectedTabIndex == 1
                                                ? AppThemes.primaryColor
                                                : Colors.grey.shade500,
                                            fontWeight: FontWeight.w800,
                                            fontSize: 18,
                                            letterSpacing: 0.5,
                                          ),
                                        ),
                                      ),
                                      // Animated indicator for selected tab
                                      if (_selectedTabIndex == 1)
                                        TweenAnimationBuilder<double>(
                                          tween: Tween<double>(
                                              begin: 0.0, end: 1.0),
                                          duration:
                                              const Duration(milliseconds: 300),
                                          curve: Curves.easeOutCubic,
                                          builder: (context, value, child) {
                                            return Positioned(
                                              bottom: 0,
                                              left: 0,
                                              right: 0,
                                              child: Container(
                                                height: 3,
                                                decoration: BoxDecoration(
                                                  color:
                                                      AppThemes.primaryColor,
                                                  borderRadius:
                                                      BorderRadius.circular(
                                                          1.5),
                                                  boxShadow: [
                                                    BoxShadow(
                                                      color: AppThemes.primaryColor
                                                          .withAlpha(76),
                                                      blurRadius: 4,
                                                      offset:
                                                          const Offset(0, 1),
                                                    ),
                                                  ],
                                                ),
                                                width: MediaQuery.of(context)
                                                        .size
                                                        .width *
                                                    0.5 *
                                                    value,
                                                margin: EdgeInsets.symmetric(
                                                  horizontal:
                                                      MediaQuery.of(context)
                                                              .size
                                                              .width *
                                                          0.25 *
                                                          (1 - value),
                                                ),
                                              ),
                                            );
                                          },
                                        ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                        // Animated indicator line
                        AnimatedContainer(
                          duration: const Duration(milliseconds: 300),
                          height: 1,
                          color: Colors.grey.withAlpha(51),
                          margin: const EdgeInsets.only(top: 0),
                        ),
                      ],
                    ),
                  ),

                  // Enhanced tab content with scrollable layout
                  // Remove fixed height to allow natural scrolling
                  AnimatedSwitcher(
                    duration: const Duration(milliseconds: 300),
                    switchInCurve: Curves.easeInOutCubic,
                    switchOutCurve: Curves.easeInOutCubic,
                    child: _selectedTabIndex == 0
                        ? _buildChargerTab(
                            _connectorsCache[_station?.uid ?? widget.uid] ?? [])
                        : _buildReviewsTab(),
                  ),
                ],
              ),
            ),
          ],
        ),
        // Bottom bar is already defined at the top level Scaffold
      ),
    );
  }

  // Enhanced station info card with modern design and review section
  Widget _buildStationInfoCard(
    String stationName,
    String address,
    String openStatus,
    String operatingHours,
  ) {
    // Get rating and review count from the station data
    final double rating = _station!.rating;
    final int reviewCount = _station!.reviews;

    // CRITICAL: Use real open status from API, not computed isAvailable
    final bool? apiOpenStatus = _station!.openStatus;
    final bool isOpenStatus = apiOpenStatus ??
        false; // Default to false if API doesn't provide status
    final String openingTimes = operatingHours;

    return Container(
      margin: const EdgeInsets.fromLTRB(16, 16, 16, 24),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).brightness == Brightness.dark
            ? Colors.grey.shade900
            : Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).brightness == Brightness.dark
                ? Colors.black.withAlpha(30)
                : Colors.black.withAlpha(13),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Text(
                  stationName,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 18,
                    color: Theme.of(context).brightness == Brightness.dark
                        ? Colors.white
                        : Colors.black,
                  ),
                ),
              ),
              // Action buttons
              Row(
                children: [
                  _buildActionButton(Icons.directions, Colors.blue),
                  const SizedBox(width: 8),
                  _buildActionButton(Icons.call, Colors.green),
                ],
              ),
            ],
          ),
          const SizedBox(height: 12),
          // Address with icon
          Row(
            children: [
              Icon(
                Icons.location_on,
                size: 16,
                color: Theme.of(context).brightness == Brightness.dark
                    ? Colors.grey.shade400
                    : Colors.grey.shade600,
              ),
              const SizedBox(width: 4),
              Expanded(
                child: Text(
                  address,
                  style: TextStyle(
                    color: Theme.of(context).brightness == Brightness.dark
                        ? Colors.grey.shade300
                        : Colors.grey.shade700,
                    fontSize: 14,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          // Status row with enhanced styling and review section
          Row(
            children: [
              // Open status indicator - only show if API provides status
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: apiOpenStatus != null
                      ? (isOpenStatus
                          ? (Theme.of(context).brightness == Brightness.dark
                              ? AppThemes.primaryColor.withAlpha(50)
                              : AppThemes.primaryColor.withAlpha(25))
                          : (Theme.of(context).brightness == Brightness.dark
                              ? Colors.red.withAlpha(50)
                              : Colors.red.withAlpha(25)))
                      : (Theme.of(context).brightness == Brightness.dark
                          ? Colors.grey.withAlpha(50)
                          : Colors.grey.withAlpha(25)),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Row(
                  children: [
                    Icon(
                      apiOpenStatus != null
                          ? (isOpenStatus ? Icons.check_circle : Icons.cancel)
                          : Icons.help_outline,
                      size: 14,
                      color: apiOpenStatus != null
                          ? (isOpenStatus ? AppThemes.primaryColor : Colors.red)
                          : Colors.grey,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      apiOpenStatus != null
                          ? (isOpenStatus ? 'Open Now' : 'Closed')
                          : 'Status unknown',
                      style: TextStyle(
                        color: apiOpenStatus != null
                            ? (isOpenStatus ? AppThemes.primaryColor : Colors.red)
                            : Colors.grey,
                        fontWeight: FontWeight.w600,
                        fontSize: 13,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 12),
              // Operating hours
              Icon(
                Icons.access_time,
                size: 14,
                color: Theme.of(context).brightness == Brightness.dark
                    ? Colors.grey.shade400
                    : Colors.grey.shade600,
              ),
              const SizedBox(width: 4),
              Text(
                openingTimes,
                style: TextStyle(
                  color: Theme.of(context).brightness == Brightness.dark
                      ? Colors.grey.shade300
                      : Colors.grey.shade600,
                ),
              ),
              // Spacer to push review section to the right
              const Spacer(),
              // Review section - clickable to navigate to reviews tab
              GestureDetector(
                onTap: () {
                  // Add haptic feedback for better user experience
                  HapticFeedback.lightImpact();
                  // Navigate to reviews tab
                  _tabController.animateTo(1);
                },
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.amber.withAlpha(30),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Row(
                    children: [
                      const Icon(
                        Icons.star,
                        size: 14,
                        color: Colors.amber,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        '$rating ($reviewCount)',
                        style: const TextStyle(
                          color: Colors.amber,
                          fontWeight: FontWeight.w600,
                          fontSize: 13,
                        ),
                      ),
                      // Removed arrow icon as requested
                    ],
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // Small action button for directions, call, etc. with dark mode support
  Widget _buildActionButton(IconData icon, Color color) {
    // Determine the action based on the icon
    void Function()? onTap;
    String url;

    if (icon == Icons.directions) {
      // Get directions URL using Google Maps
      url = _station != null
          ? 'https://www.google.com/maps/dir/?api=1&destination=${_station!.latitude},${_station!.longitude}'
          : 'https://www.google.com/maps';
      onTap = () => _launchUrl(url);
    } else if (icon == Icons.call) {
      // COMPLETELY DISABLED: Call functionality is disabled because API doesn't provide phone numbers
      // No action will be performed when call icon is tapped - no navigation anywhere
      onTap = null; // Disable the call functionality completely
      debugPrint('🚫 CALL DISABLED: Call icon tapped but functionality is disabled');
    } else {
      onTap = () {};
    }

    // Adjust color alpha based on theme
    final int alphaValue =
        Theme.of(context).brightness == Brightness.dark ? 40 : 25;

    // Adjust opacity for disabled call button
    final bool isCallButton = icon == Icons.call;
    final double opacity = isCallButton ? 0.5 : 1.0; // Make call button appear disabled

    return Opacity(
      opacity: opacity,
      child: InkWell(
        onTap: onTap, // Will be null for call icons, making them completely non-interactive
        borderRadius: onTap != null ? BorderRadius.circular(8) : null, // Remove border radius for disabled buttons
        splashColor: onTap != null ? null : Colors.transparent, // Remove splash effect for disabled buttons
        highlightColor: onTap != null ? null : Colors.transparent, // Remove highlight effect for disabled buttons
        child: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: color.withAlpha(alphaValue),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            color: color,
            size: 20,
          ),
        ),
      ),
    );
  }

  // Helper methods removed - using ConnectorUtils from utils/connector_utils.dart instead

  // Helper method to launch URLs
  Future<void> _launchUrl(String urlString) async {
    final Uri url = Uri.parse(urlString);
    try {
      // Use launchUrl with mode that works best for the platform
      await launchUrl(
        url,
        mode: LaunchMode.externalApplication,
      );
    } catch (e) {
      // Check if widget is still mounted before using context
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Could not launch: $e')),
        );
      }
    }
  }

  //
// SECTION 2: CONNECTOR SELECTION
//

  /// Enhanced charger tab with modern UI design
  /// Shows the list of available connectors grouped by type
  Widget _buildChargerTab(List<dynamic> connectors) {
    // Handle empty connectors list
    if (connectors.isEmpty) {
      return const Center(
        child: Padding(
          padding: EdgeInsets.all(16.0),
          child: Text(
            'No connector information available',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: Colors.grey,
            ),
          ),
        ),
      );
    }

    // Convert to station_details.Connector if needed
    List<station_details.Connector> stationConnectors;

    // Ensure we're working with station_details.Connector objects
    if (connectors.first is station_details.Connector) {
      stationConnectors = connectors.cast<station_details.Connector>();
    } else {
      // This should not happen, but just in case, log an error and return an empty view
      debugPrint('Error: Unexpected connector type in _buildChargerTab');
      return const Center(
        child: Padding(
          padding: EdgeInsets.all(16.0),
          child: Text(
            'Error loading connector information',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: Colors.red,
            ),
          ),
        ),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Group connectors by type
          ..._buildConnectorGroups(stationConnectors),
        ],
      ),
    );
  }

  // Group connectors by type (AC, DC, etc.) with space-efficient design
  List<Widget> _buildConnectorGroups(
      List<station_details.Connector> connectors) {
    // Debug logging for connector data
    debugPrint('=== CONNECTOR DATA DEBUG ===');
    debugPrint('Input connectors count: ${connectors.length}');
    for (var i = 0; i < connectors.length; i++) {
      final conn = connectors[i];
      debugPrint('Connector $i: type=${conn.type}, status=${conn.status}, '
          'label=${conn.label}, maxPower=${conn.maxElectricPower}, '
          'evsesUid=${conn.evsesUid}');
    }

    // CRITICAL FIX: Handle empty connectors list gracefully
    if (connectors.isEmpty) {
      return [
        Container(
          margin: const EdgeInsets.all(16),
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surfaceContainerHighest,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color:
                  Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
            ),
          ),
          child: Column(
            children: [
              Icon(
                Icons.electrical_services_outlined,
                size: 48,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
              const SizedBox(height: 16),
              Text(
                'No Connectors Available',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: Theme.of(context).colorScheme.onSurface,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'This station is currently being updated. Please check back later or contact support.',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 14,
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
              ),
            ],
          ),
        ),
      ];
    }

    // Get connectors from cache if available
    final cacheKey = _station?.uid ?? widget.uid; // Use UID for cache key
    List<station_details.Connector> cachedConnectors = connectors;

    // Use cached connectors if available
    if (_connectorsCache.containsKey(cacheKey)) {
      cachedConnectors = _connectorsCache[cacheKey]!;
      debugPrint(
          'Using ${cachedConnectors.length} cached connectors for UID $cacheKey');
    } else {
      // Cache the connectors for future use
      _connectorsCache[cacheKey] = connectors;
      debugPrint('Cached ${connectors.length} connectors for UID $cacheKey');
    }

    // Group connectors by their EVSE object key instead of type
    // Each station has multiple EVSE objects (e.g., "IN*EPL*E1719484988_1")
    // Each EVSE object represents a physical charger unit with its own connectors
    final Map<String, List<station_details.Connector>> groupedConnectors = {};

    // CRITICAL FIX: Create mapping from charging EVSE UID to EVSE key for proper UI grouping
    final Map<String, String> evseUidToKeyMap = {};
    if (_rawStationData?.evses != null) {
      _rawStationData!.evses!.forEach((evseKey, evseData) {
        if (evseData.connectors != null) {
          for (var connectorData in evseData.connectors!) {
            if (connectorData.evsesUid != null) {
              evseUidToKeyMap[connectorData.evsesUid!] = evseKey;
            }
          }
        }
      });
    }

    for (var connector in cachedConnectors) {
      // CRITICAL FIX: Use EVSE key for UI grouping, not charging EVSE UID
      // Find the EVSE key that corresponds to this connector's charging EVSE UID
      final chargingEvseUid = connector.evsesUid ?? 'Unknown_EVSE';
      final evseKeyForGrouping =
          evseUidToKeyMap[chargingEvseUid] ?? chargingEvseUid;

      if (!groupedConnectors.containsKey(evseKeyForGrouping)) {
        groupedConnectors[evseKeyForGrouping] = [];
      }
      groupedConnectors[evseKeyForGrouping]!.add(connector);
    }

    // Summary-level debug logging for grouped connectors (reduced noise)
    debugPrint(
        'Background refresh: Grouped ${groupedConnectors.length} EVSE groups with ${cachedConnectors.length} total connectors');

    List<Widget> groups = [];

    groupedConnectors.forEach((evseKeyForGrouping, connectorList) {
      // CRITICAL FIX: Use the EVSE key directly for display since we're already grouping by it
      String evseObjectKey = evseKeyForGrouping;
      String evseName = 'EVSE $evseKeyForGrouping';
      String? evseLastUsed;

      // Get EVSE details from raw station data using the EVSE key
      if (_rawStationData?.evses != null &&
          _rawStationData!.evses!.containsKey(evseKeyForGrouping)) {
        final evseData = _rawStationData!.evses![evseKeyForGrouping]!;
        evseName = evseData.name ?? 'EVSE $evseKeyForGrouping';
        evseLastUsed = evseData.lastUsed;
      }

      // Define colors based on EVSE power output type
      final Color primaryColor = Theme.of(context).colorScheme.primary;

      // Connector information is now displayed directly in the connector cards
      groups.add(
        Container(
          margin: const EdgeInsets.only(bottom: 16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Compact connector type card with specs and integrated connector list
              Container(
                margin: const EdgeInsets.symmetric(horizontal: 4),
                decoration: BoxDecoration(
                  color: Theme.of(context).brightness == Brightness.dark
                      ? Colors.grey.shade900
                      : Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: Theme.of(context).brightness == Brightness.dark
                        ? Colors.grey.withAlpha(60)
                        : Colors.grey.withAlpha(40),
                  ),
                ),
                child: Column(
                  children: [
                    // Header with connector type and availability - dynamic height
                    Container(
                      constraints: const BoxConstraints(
                        minHeight: 64, // Minimum height for single line text
                      ),
                      padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 10), // Slightly reduced vertical padding
                      decoration: BoxDecoration(
                        color: Theme.of(context).brightness == Brightness.dark
                            ? const Color(
                                0xFF2A2A2A) // Dark grayish background for dark mode
                            : const Color(
                                0xFFF8F9FA), // Light grayish-blue background extracted from image
                        borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(11),
                          topRight: Radius.circular(11),
                        ),
                      ),
                      child: Column(
                        children: [
                          // Main row: EV plug icon and EVSE name with right-side elements
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              // Connector plug icon (matching the specific icon in the image)
                              Container(
                                width: 36,
                                height: 36,
                                decoration: BoxDecoration(
                                  color: Colors
                                      .white, // White background for icon container
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Center(
                                  child: Icon(
                                    Icons
                                        .electrical_services, // Connector plug icon matching the image
                                    color: const Color(
                                        0xFF43A047), // Green icon color
                                    size: 22,
                                  ),
                                ),
                              ),
                              const SizedBox(width: 12),
                              // Left side: EVSE name section
                              Expanded(
                                flex: 2, // Takes 2/5 of available space
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    // Use dynamic station name from API with reduced font size
                                    Container(
                                      constraints: const BoxConstraints(
                                        minHeight:
                                            20, // Minimum height for single line
                                      ),
                                      child: Text(
                                        evseName, // Use EVSE name as header
                                        style: TextStyle(
                                          fontWeight: FontWeight.bold,
                                          fontSize:
                                              15, // Increased font size for better visibility
                                          color: Theme.of(context).brightness ==
                                                  Brightness.dark
                                              ? Colors.white
                                              : Colors.grey.shade800,
                                        ),
                                        maxLines:
                                            3, // Allow text wrapping to 3 lines for long names
                                        overflow: TextOverflow.ellipsis,
                                        softWrap: true, // Enable soft wrapping
                                      ),
                                    ),
                                  ],
                                ),
                              ),

                              // Invisible spacer to create separation (increased width)
                              const SizedBox(width: 16),

                              // Right side: Last used and analytics (preserved exactly as before)
                              Expanded(
                                flex:
                                    3, // Takes 3/5 of available space (increased from 2)
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.end,
                                  mainAxisAlignment: MainAxisAlignment
                                      .start, // Changed from spaceBetween to start
                                  children: [
                                    // Last used date - positioned at top right (conditional rendering)
                                    if (_station!.connectors.isNotEmpty && _isLastUsedDataValid(evseLastUsed))
                                      Container(
                                        padding: const EdgeInsets.symmetric(
                                            horizontal: 6, vertical: 3),
                                        decoration: BoxDecoration(
                                          color: Theme.of(context).brightness ==
                                                  Brightness.dark
                                              ? Colors.blue.withAlpha(40)
                                              : Colors.blue.withAlpha(25),
                                          borderRadius:
                                              BorderRadius.circular(8),
                                        ),
                                        child: Row(
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            Icon(
                                              Icons.access_time,
                                              size: 12,
                                              color: Colors.blue,
                                            ),
                                            const SizedBox(width: 3),
                                            Text(
                                              'Last used: ${_formatRelativeDate(evseLastUsed!)}',
                                              style: const TextStyle(
                                                fontWeight: FontWeight.w500,
                                                fontSize:
                                                    11, // Increased font size
                                                color: Colors.blue,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),

                                    // Add proper vertical spacing between Last used and View Analytics
                                    if (_station!.connectors.isNotEmpty && _isLastUsedDataValid(evseLastUsed))
                                      const SizedBox(
                                          height: 12), // Explicit spacing

                                    /*
                                    TEMPORARILY DISABLED: View Analytics button

                                    The analytics feature is currently disabled because the backend
                                    data integration is not yet implemented. This button and its
                                    navigation functionality are commented out to hide the feature
                                    from users until proper analytics data is available.

                                    To re-enable: Simply uncomment this entire block when analytics
                                    data backend is ready.
                                    */

                                    // View Analytics button - positioned at bottom right
                                    /* if (_station!.connectors.isNotEmpty)
                                      GestureDetector(
                                        onTap: () {
                                          // CRITICAL FIX: Add error handling for analytics navigation
                                          try {
                                            Navigator.push(
                                              context,
                                              MaterialPageRoute(
                                                builder: (context) =>
                                                    ConnectorAnalyticsPage(
                                                  connectorType: evseObjectKey,
                                                  stationName: _cleanStationName(_station?.name ?? 'Station'),
                                                  stationUid: _station?.uid ??
                                                      widget.uid,
                                                ),
                                              ),
                                            );
                                          } catch (e) {
                                            debugPrint(
                                                'Error navigating to analytics: $e');
                                            // Show user-friendly message
                                            ScaffoldMessenger.of(context)
                                                .showSnackBar(
                                              SnackBar(
                                                content: Text(
                                                    'Analytics feature is currently unavailable'),
                                                duration: Duration(seconds: 2),
                                              ),
                                            );
                                          }
                                        },
                                        child: Container(
                                          padding: const EdgeInsets.symmetric(
                                              horizontal: 10, vertical: 6),
                                          decoration: BoxDecoration(
                                            color: Theme.of(context)
                                                        .brightness ==
                                                    Brightness.dark
                                                ? primaryColor.withAlpha(40)
                                                : primaryColor.withAlpha(25),
                                            borderRadius:
                                                BorderRadius.circular(10),
                                          ),
                                          child: Row(
                                            mainAxisSize: MainAxisSize.min,
                                            children: [
                                              Icon(
                                                Icons
                                                    .analytics_outlined, // Analytics icon
                                                size: 16,
                                                color: primaryColor,
                                              ),
                                              const SizedBox(width: 4),
                                              Text(
                                                'View Analytics',
                                                style: TextStyle(
                                                  fontWeight: FontWeight.w500,
                                                  fontSize: 13,
                                                  color: primaryColor,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ), */
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),

                    // Connector list section with optimized spacing
                    Padding(
                      padding: const EdgeInsets.fromLTRB(16, 8, 16, 0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Connector section header
                          Row(
                            children: [
                              Icon(
                                Icons.electric_bolt,
                                color: primaryColor,
                                size: 18,
                              ),
                              const SizedBox(width: 8),
                              Text(
                                'Available Connectors',
                                style: TextStyle(
                                  fontSize: 15,
                                  fontWeight: FontWeight.w600,
                                  color: primaryColor,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(
                              height: 8), // Slightly increased spacing
                        ],
                      ),
                    ),

                    // Integrated connector list directly in the card
                    Padding(
                      padding: const EdgeInsets.fromLTRB(
                          16, 0, 16, 16), // Adjusted padding
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Connector cards
                          ...connectorList.map((conn) =>
                              _buildConnectorCard(conn, evseObjectKey)),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      );
    });

    return groups;
  }

  /// Check if last used data is valid and should be displayed
  /// Returns true only if the data is not null, not empty, and contains valid date/time data
  bool _isLastUsedDataValid(String? lastUsedData) {
    if (lastUsedData == null || lastUsedData.isEmpty) {
      debugPrint('📅 Last used data validation: null or empty - hiding UI');
      return false;
    }

    try {
      // Try to parse the date to validate it's a proper date/time string
      final DateTime parsedDate = DateTime.parse(lastUsedData);
      debugPrint('📅 Last used data validation: valid date "$lastUsedData" -> $parsedDate');
      return true;
    } catch (e) {
      debugPrint('📅 Last used data validation: invalid date format "$lastUsedData" - hiding UI');
      return false;
    }
  }

  /// Convert authentic API date string to relative time format
  /// Examples: "Today", "1 day ago", "3 days ago"
  String _formatRelativeDate(String apiDateString) {
    try {
      final DateTime apiDate = DateTime.parse(apiDateString);
      final DateTime now = DateTime.now();
      final Duration difference = now.difference(apiDate);

      if (difference.inDays == 0) {
        return 'Today';
      } else if (difference.inDays == 1) {
        return '1 day ago';
      } else if (difference.inDays > 1) {
        return '${difference.inDays} days ago';
      } else {
        // Future date (shouldn't happen for "last used" but handle gracefully)
        return 'Recently';
      }
    } catch (e) {
      debugPrint('Error parsing API date: $apiDateString, error: $e');
      return ''; // Return empty string if date parsing fails
    }
  }

  /// Compact connector card with space-efficient design
  /// Shows connector details in a more compact layout
  Widget _buildConnectorCard(
      station_details.Connector connector, String evseObjectKey) {
    // Extract connector details with proper null checks
    final connectorName = connector.label ?? connector.type ?? '';
    final connectorType = connector.type ?? '';

    // Use authentic API priceLabel directly
    String formattedPriceLabel = connector.priceLabel ?? '';

    // Display raw API power data directly - CRITICAL FIX: Use kW not kWh
    // maxElectricPower represents power capacity in kilowatts (kW), not energy in kilowatt-hours (kWh)
    String powerDisplay = '';
    if (connector.maxElectricPower != null && connector.maxElectricPower! > 0) {
      // CRITICAL DEBUG: Log the exact raw value from API
      debugPrint(
          '🔍 CRITICAL DEBUG: Raw maxElectricPower from API: ${connector.maxElectricPower}');
      debugPrint(
          '🔍 CRITICAL DEBUG: maxElectricPower type: ${connector.maxElectricPower.runtimeType}');

      // Use the raw API value directly preserving exact format
      powerDisplay = '${connector.maxElectricPower} kW';

      debugPrint(
          '✅ CRITICAL DEBUG: Station detail displaying EXACT API power: ${connector.maxElectricPower} kW for connector ${connector.connectorId}');
      debugPrint(
          '✅ CRITICAL DEBUG: Final powerDisplay string: "$powerDisplay"');
    } else {
      debugPrint(
          '❌ CRITICAL DEBUG: No power data available for connector ${connector.connectorId}');
      debugPrint(
          '❌ CRITICAL DEBUG: maxElectricPower value: ${connector.maxElectricPower}');
    }

    // Get power type from connector-specific data (prioritizes connector over EVSE data)
    final String powerType = _getPowerTypeFromEvse(connector);

    // Check if connector is selectable based on 5 specific status types
    debugPrint('🔍 BUILDING CONNECTOR UI: ${connector.type} (ID: ${connector.connectorId})');
    debugPrint('🔍 Raw connector status: "${connector.status}" (${connector.status.runtimeType})');
    debugPrint('🔍 Connector label: "${connector.label}"');
    debugPrint('🔍 Connector EVSE UID: "${connector.evsesUid}"');

    final bool isSelectable = _isConnectorSelectable(connector.status);
    debugPrint('🔍 CONNECTOR SELECTABILITY: isSelectable = $isSelectable');

    // Test the status display methods
    final statusColor = _getStatusColor(connector.status);
    final statusText = _getStatusText(connector.status);
    debugPrint('🔍 STATUS DISPLAY: color = $statusColor, text = "$statusText"');

    // Check if this connector is selected
    final isSelected = (_selectedConnector == connector);

    // Get connector color based on status (yellow for "gun connected", blue for others)
    final Color primaryColor = _getConnectorColor(connector.status);

    // Check if gun is connected for special yellow styling
    final bool isGunConnected = connector.status?.toLowerCase().trim() == 'gun connected';

    // Check if connector is charging for special charging styling
    final bool isCharging = _isConnectorCharging(connector.status);

    return GestureDetector(
      onTap: () {
        if (isSelectable) {
          setState(() {
            // Toggle selection - if already selected, deselect it
            if (_selectedConnector == connector) {
              _selectedConnector = null;
            } else {
              _selectedConnector = connector;
            }
          });
        } else {
          // Show specific status messages based on connector status
          String statusMessage = _getConnectorStatusMessage(connector.status);
          _showConnectorStatusSnackbar(statusMessage);
        }
      },
      child: AnimatedContainer(
            duration: const Duration(milliseconds: 250),
            margin: const EdgeInsets.only(bottom: 4), // Reduced bottom margin
            padding: EdgeInsets.fromLTRB(
              (isCharging || isGunConnected) ? 16 : 12, // Enhanced left padding for special statuses (left edge indicator space)
              (isCharging || isGunConnected) ? 12 : 8,  // Enhanced top padding for special statuses
              12, // Standard right padding
              (isCharging || isGunConnected) ? 8 : 8,   // Standard bottom padding
            ),
            decoration: (isCharging || isGunConnected)
                ? _buildSpecialStatusDecoration(context, connector.status) // Special decoration for charging and gun connected
                : BoxDecoration(
                color: (Theme.of(context).brightness == Brightness.dark
                    ? (isSelected
                        ? primaryColor.withAlpha(30)
                        : (isSelectable
                            ? Colors.grey.shade900
                            : Colors.grey.shade900.withAlpha(120)))
                    : (isSelected
                        ? primaryColor.withAlpha(15)
                        : (isSelectable ? Colors.white : Colors.grey.shade100))),
                borderRadius: BorderRadius.circular(10),
                border: Border.all(
                  color: (isSelected
                      ? primaryColor
                      : (isSelectable
                          ? Colors.grey.withAlpha(
                              Theme.of(context).brightness == Brightness.dark
                                  ? 60
                                  : 30)
                          : Colors.red.withAlpha(
                              Theme.of(context).brightness == Brightness.dark
                                      ? 80
                                      : 50))),
                  width: (isSelected ? 1.5 : (isSelectable ? 1 : 1.5)),
                ),
              ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Connector icon
            Container(
              width: 44, // Increased size
              height: 44, // Increased size
              decoration: BoxDecoration(
                color: (isCharging || isGunConnected)
                    ? _getBadgeColor(connector.status).withAlpha(
                        Theme.of(context).brightness == Brightness.dark ? 40 : 20)
                    : (Theme.of(context).brightness == Brightness.dark
                        ? (isSelected
                            ? primaryColor.withAlpha(40)
                            : (isSelectable
                                ? Colors.grey.shade800
                                : Colors.grey.shade800.withAlpha(100)))
                        : (isSelected
                            ? primaryColor.withAlpha(20)
                            : (isSelectable
                                ? Colors.grey.withAlpha(15)
                                : Colors.grey.withAlpha(40)))),
                borderRadius:
                    BorderRadius.circular(10), // Slightly larger radius
                border: Border.all(
                  color: (isCharging || isGunConnected)
                      ? _getBadgeColor(connector.status).withAlpha(
                          Theme.of(context).brightness == Brightness.dark ? 200 : 150)
                      : (isSelected
                          ? primaryColor.withAlpha(
                              Theme.of(context).brightness == Brightness.dark
                                  ? 150
                                  : 100)
                          : (isSelectable
                              ? Colors.transparent
                              : Colors.red.withAlpha(60))),
                  width: (isCharging || isGunConnected) ? 1.5 : (isSelected ? 1.5 : (isSelectable ? 0 : 1)),
                ),
              ),
              child: Padding(
                padding: const EdgeInsets.all(
                    5.0), // Reduced padding to fit larger image
                child: _buildConnectorIcon(
                  context: context,
                  connector: connector,
                  isSelected: isSelected,
                  primaryColor: primaryColor,
                ),
              ),
            ),
            const SizedBox(width: 10),
            // Connector details - more compact
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Connector name and power on the same row
                  Row(
                    children: [
                      // Connector name - ONLY show if authentic API data exists
                      if (connectorName.isNotEmpty)
                        Flexible(
                          child: Text(
                            connectorName, // Use ONLY authentic connector name from API
                            style: TextStyle(
                              fontWeight: FontWeight.w600, // Slightly lighter
                              fontSize: 13,
                              color: isSelectable
                                  ? (Theme.of(context).brightness ==
                                          Brightness.dark
                                      ? Colors.white
                                      : Colors.grey.shade800)
                                  : (Theme.of(context).brightness ==
                                          Brightness.dark
                                      ? Colors.grey.shade500
                                      : Colors.grey.shade500),
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      // Dot separator - only show if both name and power exist
                      if (connectorName.isNotEmpty && powerDisplay.isNotEmpty)
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 4.0),
                          child: Text(
                            "•",
                            style: TextStyle(
                              color: Theme.of(context).brightness ==
                                      Brightness.dark
                                  ? Colors.grey.shade400
                                  : Colors.grey.shade600,
                            ),
                          ),
                        ),
                      // Power output - using ONLY raw API data, no transformations
                      if (powerDisplay.isNotEmpty)
                        Text(
                          powerDisplay, // Show ONLY authentic kW value from API
                          style: TextStyle(
                            fontSize: 13,
                            fontWeight: FontWeight.w500,
                            color: isSelectable
                                ? primaryColor
                                : Colors.grey.shade500,
                          ),
                        ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  // State of charge removed from here - moved to selection tick area
                  const SizedBox(height: 2),
                  // Price with connector type and power type badges
                  Flexible(
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // Small connector type badge - ONLY show if authentic API data exists
                        if (connectorType.isNotEmpty)
                          Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 6, vertical: 2),
                            decoration: BoxDecoration(
                              color: _getBadgeColor(connector.status).withAlpha(
                                  Theme.of(context).brightness ==
                                          Brightness.dark
                                      ? 50
                                      : 30),
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Text(
                              connectorType, // Use ONLY authentic connector type from API
                              style: TextStyle(
                                fontSize: 10,
                                fontWeight: FontWeight.w500,
                                color: _getBadgeColor(connector.status), // Status-based color for badges
                              ),
                              maxLines: 1,
                            ),
                          ),
                        // Power type badge (AC/DC/DCAC) - only show if available
                        if (powerType.isNotEmpty) ...[
                          const SizedBox(width: 4),
                          Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 6, vertical: 2),
                            decoration: BoxDecoration(
                              color: _getBadgeColor(connector.status).withAlpha(
                                  Theme.of(context).brightness ==
                                          Brightness.dark
                                      ? 50
                                      : 30),
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Text(
                              powerType,
                              style: TextStyle(
                                fontSize: 10,
                                fontWeight: FontWeight.w500,
                                color: _getBadgeColor(connector.status), // Status-based color for badges
                              ),
                              maxLines: 1,
                            ),
                          ),
                        ],
                        const SizedBox(width: 6),
                        // Price with label if available - ONLY show authentic API data
                        if (formattedPriceLabel.isNotEmpty)
                          Flexible(
                            child: Text(
                              formattedPriceLabel, // Use ONLY authentic API priceLabel
                              style: TextStyle(
                                fontSize: 12,
                                color: Theme.of(context).brightness ==
                                        Brightness.dark
                                    ? Colors.grey.shade300
                                    : Colors.grey.shade600,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            // Status and selection in one column
            Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Availability status with actual connector status
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: _getBadgeColor(connector.status).withAlpha(
                        Theme.of(context).brightness == Brightness.dark
                            ? 40
                            : 25), // Status-based background color
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    _getStatusText(connector.status),
                    style: TextStyle(
                      fontSize: 11,
                      color: _getBadgeColor(connector.status), // Use status-based badge color
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                // Selection indicator or charging info
                if (isSelected)
                  Container(
                    margin: const EdgeInsets.only(top: 4),
                    height: 20,
                    width: 20,
                    decoration: BoxDecoration(
                      color: _getBadgeColor(connector.status), // Status-based tick color
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.check,
                      size: 14,
                      color: Colors.white,
                    ),
                  )
                else if (connector.soc != null && connector.status?.toLowerCase().trim() == 'charging')
                  // Battery icon and SOC for charging connectors (relocated from main content area)
                  Container(
                    margin: const EdgeInsets.only(top: 4),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.battery_charging_full,
                          size: 12,
                          color: _getBadgeColor(connector.status), // Status-based battery icon color
                        ),
                        const SizedBox(width: 2),
                        Text(
                          '${connector.soc}%',
                          style: TextStyle(
                            fontSize: 10,
                            color: _getBadgeColor(connector.status), // Status-based SOC text color
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // Enhanced reviews tab with full functionality and dark mode support
  Widget _buildReviewsTab() {
    // Get rating from the station data
    final double rating = _station?.rating ?? 0.0;
    final String ratingText = rating.toStringAsFixed(1);

    // Calculate rating distribution from actual review data
    final Map<int, double> ratingDistribution = _calculateRatingDistribution();

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Rating summary
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Theme.of(context).brightness == Brightness.dark
                  ? Colors.grey.shade900
                  : Colors.white,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Theme.of(context).brightness == Brightness.dark
                      ? Colors.black.withAlpha(30)
                      : Colors.black.withAlpha(13),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Row(
              children: [
                // Average rating
                Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    color: Colors.green.withAlpha(
                        Theme.of(context).brightness == Brightness.dark
                            ? 40
                            : 25),
                    shape: BoxShape.circle,
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        ratingText,
                        style: const TextStyle(
                          fontSize: 26,
                          fontWeight: FontWeight.bold,
                          color: Colors.green,
                        ),
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: List.generate(5, (index) {
                          return Icon(
                            index < rating.floor()
                                ? Icons.star
                                : Icons.star_border,
                            color: Colors.amber,
                            size: 12,
                          );
                        }),
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 16),
                // Rating bars - now using actual review data
                Expanded(
                  child: Column(
                    children: [
                      // Calculate ratios from actual review data
                      _buildRatingBar(5, ratingDistribution[5] ?? 0.0),
                      _buildRatingBar(4, ratingDistribution[4] ?? 0.0),
                      _buildRatingBar(3, ratingDistribution[3] ?? 0.0),
                      _buildRatingBar(2, ratingDistribution[2] ?? 0.0),
                      _buildRatingBar(1, ratingDistribution[1] ?? 0.0),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // Write a review button
          Container(
            margin: const EdgeInsets.symmetric(vertical: 16),
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: () => _showAddReviewDialog(),
              icon: const Icon(Icons.rate_review, color: Colors.white),
              label: const Text(
                'Write a Review',
                style: TextStyle(color: Colors.white),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppThemes.primaryColor,
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ),

          // Review list with optimized loading state
          if (_isLoadingReviews)
            // Compact loading indicator that doesn't take up too much space
            Container(
              height: 100,
              alignment: Alignment.center,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SizedBox(
                    width: 24,
                    height: 24,
                    child: CircularProgressIndicator(
                      strokeWidth: 2.0,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                  ),
                  const SizedBox(height: 12),
                  Text(
                    'Loading reviews...',
                    style: TextStyle(
                      fontSize: 14,
                      color: Theme.of(context).brightness == Brightness.dark
                          ? Colors.grey.shade400
                          : Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            )
          else if (_reviews.isEmpty)
            Center(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Text(
                  'No reviews yet. Be the first to review!',
                  style: TextStyle(
                    color: Theme.of(context).brightness == Brightness.dark
                        ? Colors.grey.shade400
                        : Colors.grey.shade600,
                  ),
                ),
              ),
            )
          else
            ..._reviews.map((review) => _buildReviewCard(review)),
        ],
      ),
    );
  }

  /// Calculate rating distribution from actual review data
  /// Returns a map where keys are star ratings (1-5) and values are ratios (0.0-1.0)
  Map<int, double> _calculateRatingDistribution() {
    // Initialize counters for each rating
    final Map<int, int> ratingCounts = {1: 0, 2: 0, 3: 0, 4: 0, 5: 0};

    // If no reviews, return empty distribution
    if (_reviews.isEmpty) {
      return {1: 0.0, 2: 0.0, 3: 0.0, 4: 0.0, 5: 0.0};
    }

    // Count reviews for each rating
    for (final review in _reviews) {
      // Extract rating from review data (same logic as in _buildReviewCard)
      double reviewRating = 0.0;
      if (review.containsKey('rate') && review['rate'] != null) {
        reviewRating = (review['rate'] as num).toDouble();
      } else if (review.containsKey('rating') && review['rating'] != null) {
        reviewRating = (review['rating'] as num).toDouble();
      }

      // Round to nearest integer and ensure it's in valid range (1-5)
      final int ratingInt = reviewRating.round().clamp(1, 5);
      ratingCounts[ratingInt] = (ratingCounts[ratingInt] ?? 0) + 1;

      debugPrint('📊 Review rating: $reviewRating -> counted as $ratingInt stars');
    }

    // Calculate total number of reviews
    final int totalReviews = _reviews.length;

    // Convert counts to ratios (0.0 to 1.0)
    final Map<int, double> ratingDistribution = {};
    for (int stars = 1; stars <= 5; stars++) {
      final int count = ratingCounts[stars] ?? 0;
      final double ratio = totalReviews > 0 ? count / totalReviews : 0.0;
      ratingDistribution[stars] = ratio;

      debugPrint('📊 $stars stars: $count reviews (${(ratio * 100).toStringAsFixed(1)}%)');
    }

    debugPrint('📊 Rating distribution calculated from $totalReviews reviews');
    return ratingDistribution;
  }

  // Removed duplicate method

  // Helper for rating bars with dark mode support
  Widget _buildRatingBar(int stars, double ratio) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          Text(
            '$stars',
            style: TextStyle(
              fontSize: 12,
              color: Theme.of(context).brightness == Brightness.dark
                  ? Colors.grey.shade400
                  : Colors.grey.shade600,
            ),
          ),
          const SizedBox(width: 4),
          Icon(Icons.star, color: Colors.amber, size: 12),
          const SizedBox(width: 8),
          Expanded(
            child: ClipRRect(
              borderRadius: BorderRadius.circular(4),
              child: LinearProgressIndicator(
                value: ratio,
                minHeight: 6,
                backgroundColor: Theme.of(context).brightness == Brightness.dark
                    ? Colors.grey.shade800
                    : Colors.grey.shade200,
                valueColor: AlwaysStoppedAnimation<Color>(AppThemes.primaryColor),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Enhanced review card with dark mode support
  Widget _buildReviewCard(Map<String, dynamic> review) {
    // Debug the review data to understand the structure
    debugPrint('Review data: ${review.toString()}');

    // Extract data from the API review format with better null handling
    // Try multiple possible field names for user name
    String userName = 'Anonymous';
    if (review.containsKey('user_name') && review['user_name'] != null) {
      userName = review['user_name'] as String;
    } else if (review.containsKey('userName') && review['userName'] != null) {
      userName = review['userName'] as String;
    } else if (review.containsKey('name') && review['name'] != null) {
      userName = review['name'] as String;
    } else if (review.containsKey('user') && review['user'] is Map) {
      final user = review['user'] as Map<String, dynamic>;
      if (user.containsKey('name') && user['name'] != null) {
        userName = user['name'] as String;
      }
    }

    // If userName is still empty, use 'Anonymous'
    if (userName.isEmpty) {
      userName = 'Anonymous';
    }

    // Create avatar initial from the first character of the user name
    final String userAvatar =
        userName.isNotEmpty ? userName[0].toUpperCase() : 'A';

    // Try multiple possible field names for rating
    double rating = 0.0;
    if (review.containsKey('rate') && review['rate'] != null) {
      rating = (review['rate'] as num).toDouble();
    } else if (review.containsKey('rating') && review['rating'] != null) {
      rating = (review['rating'] as num).toDouble();
    }

    // Get comment with fallback
    final String comment = review['comment'] as String? ?? '';

    // Try multiple possible field names for creation date
    String createdAt = '';
    if (review.containsKey('created_at') && review['created_at'] != null) {
      createdAt = review['created_at'] as String;
    } else if (review.containsKey('createdAt') && review['createdAt'] != null) {
      createdAt = review['createdAt'] as String;
    } else if (review.containsKey('timestamp') && review['timestamp'] != null) {
      createdAt = review['timestamp'] as String;
    }

    // Format the date using relative time format
    String formattedDate = '';
    try {
      // Use the same relative date formatting as last used dates
      formattedDate = _formatRelativeDate(createdAt);
    } catch (e) {
      debugPrint('Error parsing review date: $e');
      formattedDate = ''; // Return empty string if date parsing fails
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).brightness == Brightness.dark
            ? Colors.grey.shade900
            : Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).brightness == Brightness.dark
                ? Colors.black.withAlpha(30)
                : Colors.black.withAlpha(8),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Review header
          Row(
            children: [
              // User avatar
              CircleAvatar(
                backgroundColor: Colors.green.withAlpha(
                    Theme.of(context).brightness == Brightness.dark ? 40 : 50),
                child: Text(
                  userAvatar,
                  style: const TextStyle(
                    color: Colors.green,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              const SizedBox(width: 12),
              // User name and rating
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      userName,
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 15,
                        color: Theme.of(context).brightness == Brightness.dark
                            ? Colors.white
                            : Colors.black,
                      ),
                    ),
                    const SizedBox(height: 2),
                    Row(
                      children: [
                        ...List.generate(5, (index) {
                          return Icon(
                            index < rating.floor()
                                ? Icons.star
                                : Icons.star_border,
                            color: Colors.amber,
                            size: 14,
                          );
                        }),
                        const SizedBox(width: 4),
                        Text(
                          formattedDate,
                          style: TextStyle(
                            fontSize: 12,
                            color:
                                Theme.of(context).brightness == Brightness.dark
                                    ? Colors.grey.shade300
                                    : Colors.grey.shade600,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
          // Review content
          if (comment.isNotEmpty)
            Padding(
              padding: const EdgeInsets.only(top: 12, left: 4),
              child: Text(
                comment,
              ),
            ),
        ],
      ),
    );
  }

  // Show dialog to add a new review with dark mode support
  void _showAddReviewDialog() {
    int rating = 0;
    final commentController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) {
          final isDarkMode = Theme.of(context).brightness == Brightness.dark;

          return AlertDialog(
            backgroundColor: isDarkMode ? Colors.grey.shade900 : Colors.white,
            title: Text(
              'Write a Review',
              style: TextStyle(
                color: isDarkMode ? Colors.white : Colors.black,
              ),
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Rating',
                  style: TextStyle(
                    color: isDarkMode ? Colors.white : Colors.black,
                  ),
                ),
                const SizedBox(height: 8),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: List.generate(5, (index) {
                    return IconButton(
                      icon: Icon(
                        index < rating ? Icons.star : Icons.star_border,
                        color: Colors.amber,
                        size: 32,
                      ),
                      onPressed: () {
                        setState(() {
                          rating = index + 1;
                        });
                      },
                    );
                  }),
                ),
                const SizedBox(height: 16),
                Text(
                  'Comment',
                  style: TextStyle(
                    color: isDarkMode ? Colors.white : Colors.black,
                  ),
                ),
                const SizedBox(height: 8),
                TextField(
                  controller: commentController,
                  maxLines: 3,
                  style: TextStyle(
                    color: isDarkMode ? Colors.white : Colors.black,
                  ),
                  // Add proper keyboard configuration
                  keyboardType: TextInputType.text,
                  textCapitalization: TextCapitalization.sentences,
                  textInputAction: TextInputAction.done,
                  // Add focus handling
                  onEditingComplete: () {
                    // Clear focus when done editing
                    FocusScope.of(context).unfocus();
                  },
                  decoration: InputDecoration(
                    hintText: 'Share your experience...',
                    hintStyle: TextStyle(
                      color: isDarkMode
                          ? Colors.grey.shade400
                          : Colors.grey.shade600,
                    ),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(
                        color: isDarkMode
                            ? Colors.grey.shade700
                            : Colors.grey.shade300,
                      ),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(
                        color: isDarkMode
                            ? Colors.grey.shade700
                            : Colors.grey.shade300,
                      ),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(
                        color: AppThemes.primaryColor,
                      ),
                    ),
                    filled: isDarkMode,
                    fillColor: isDarkMode ? Colors.grey.shade800 : null,
                    // Add content padding to prevent text from being too close to the border
                    contentPadding: const EdgeInsets.symmetric(
                        horizontal: 12, vertical: 12),
                  ),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text(
                  'CANCEL',
                  style: TextStyle(
                    color: Colors.grey,
                  ),
                ),
              ),
              ElevatedButton(
                onPressed: () {
                  if (rating > 0 && commentController.text.isNotEmpty) {
                    // Close the dialog
                    Navigator.pop(context);

                    // Show a message that we're submitting
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Submitting review...'),
                        duration: Duration(seconds: 1),
                      ),
                    );

                    // Submit the review in a separate method to avoid BuildContext issues
                    _submitReview(rating, commentController.text);
                  } else {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Please add a rating and comment'),
                        backgroundColor: Colors.red,
                      ),
                    );
                  }
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppThemes.primaryColor,
                ),
                child: const Text(
                  'SUBMIT',
                  style: TextStyle(
                    color: Colors.white,
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  // Submit a review using ReviewService
  Future<void> _submitReview(int rating, String comment) async {
    try {
      // Use the location_id from the widget.uid (same as used for fetching station details)
      final String locationId = widget.uid;

      if (locationId.isEmpty) {
        _showErrorMessage('Cannot submit review: Location ID is missing');
        return;
      }

      debugPrint('🌟 Submitting review for location ID: $locationId');

      // Generate tags based on rating
      final List<String> tags = _reviewService.generateTagsFromRating(rating);

      // Submit review using ReviewService
      final response = await _reviewService.submitReview(
        locationId: locationId,
        rating: rating,
        comment: comment,
        tags: tags,
      );

      // Handle response
      if (response.success) {
        debugPrint('✅ Review submitted successfully');
        if (mounted) {
          _showSuccessMessage('Review submitted successfully!');
          // Refresh reviews to show the new review
          _fetchReviews();
        }
      } else {
        throw Exception(response.message ?? 'Failed to submit review');
      }
    } catch (e) {
      debugPrint('❌ Error submitting review: $e');
      if (mounted) {
        _showErrorMessage('Error: ${e.toString()}');
      }
    }
  }

  // Show a success message
  void _showSuccessMessage(String message) {
    if (!mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: const Color(0xFF43A047),
      ),
    );
  }

  // Show an error message
  void _showErrorMessage(String message) {
    if (!mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  // Helper method to get color based on connector status
  Color _getStatusColor(String? status) {
    if (status == null || status.isEmpty) {
      debugPrint('⚠️ Connector status is null or empty, using grey color');
      return Colors.grey;
    }

    // Log the exact status for debugging
    debugPrint('🎨 Getting color for connector status: "$status"');

    final statusLower = status.toLowerCase().trim();

    // Use exact matching for known API status values first
    switch (statusLower) {
      case 'available':
        debugPrint('🎨 Status "$status" -> Green (Available)');
        return AppThemes.primaryColor;
      case 'gun connected':
        debugPrint('🎨 Status "$status" -> Green (Gun Connected)');
        return AppThemes.primaryColor;
      case 'charging':
        debugPrint('🎨 Status "$status" -> Orange (Charging)');
        return Colors.orange;
      case 'unavailable':
        debugPrint('🎨 Status "$status" -> Red (Unavailable)');
        return Colors.red;
      case 'offline':
        debugPrint('🎨 Status "$status" -> Red (Offline)');
        return Colors.red;
      case 'faulted':
      case 'error':
        debugPrint('🎨 Status "$status" -> Red (Faulted/Error)');
        return Colors.red;
      case 'preparing':
        debugPrint('🎨 Status "$status" -> Blue (Preparing)');
        return Colors.blue;
      case 'reserved':
        debugPrint('🎨 Status "$status" -> Blue (Reserved)');
        return Colors.blue;
      case 'occupied':
        debugPrint('🎨 Status "$status" -> Orange (Occupied)');
        return Colors.orange;
      default:
        // Fallback to contains() for partial matches
        if (statusLower.contains('available') || statusLower.contains('online')) {
          debugPrint('🎨 Status "$status" -> Green (contains available/online)');
          return AppThemes.primaryColor;
        } else if (statusLower.contains('charging') || statusLower.contains('busy')) {
          debugPrint('🎨 Status "$status" -> Orange (contains charging/busy)');
          return Colors.orange;
        } else if (statusLower.contains('unavailable') || statusLower.contains('offline') || statusLower.contains('fault')) {
          debugPrint('🎨 Status "$status" -> Red (contains unavailable/offline/fault)');
          return Colors.red;
        } else {
          debugPrint('🎨 Status "$status" -> Grey (unknown status)');
          return Colors.grey;
        }
    }
  }

  // Helper method to get text based on connector status
  String _getStatusText(String? status) {
    if (status == null || status.isEmpty) {
      debugPrint('⚠️ Connector status is null or empty, returning "Unknown"');
      return 'Unknown';
    }

    // Log the exact status for debugging
    debugPrint('📝 Getting text for connector status: "$status"');

    final statusLower = status.toLowerCase().trim();

    // Use exact matching for known API status values first
    switch (statusLower) {
      case 'available':
        debugPrint('📝 Status "$status" -> "Available"');
        return 'Available';
      case 'gun connected':
        debugPrint('📝 Status "$status" -> "Gun Connected"');
        return 'Gun Connected';
      case 'charging':
        debugPrint('📝 Status "$status" -> "Charging"');
        return 'Charging';
      case 'unavailable':
        debugPrint('📝 Status "$status" -> "Unavailable"');
        return 'Unavailable';
      case 'offline':
        debugPrint('📝 Status "$status" -> "Offline"');
        return 'Offline';
      case 'faulted':
        debugPrint('📝 Status "$status" -> "Faulted"');
        return 'Faulted';
      case 'error':
        debugPrint('📝 Status "$status" -> "Error"');
        return 'Error';
      case 'preparing':
        debugPrint('📝 Status "$status" -> "Preparing"');
        return 'Preparing';
      case 'reserved':
        debugPrint('📝 Status "$status" -> "Reserved"');
        return 'Reserved';
      case 'occupied':
        debugPrint('📝 Status "$status" -> "Occupied"');
        return 'Occupied';
      default:
        // Fallback to contains() for partial matches
        if (statusLower.contains('available') || statusLower.contains('online')) {
          debugPrint('📝 Status "$status" -> "Available" (contains available/online)');
          return 'Available';
        } else if (statusLower.contains('charging') || statusLower.contains('busy')) {
          debugPrint('📝 Status "$status" -> "In Use" (contains charging/busy)');
          return 'In Use';
        } else if (statusLower.contains('unavailable') || statusLower.contains('offline') || statusLower.contains('fault')) {
          debugPrint('📝 Status "$status" -> "Unavailable" (contains unavailable/offline/fault)');
          return 'Unavailable';
        } else {
          // Return the original status with proper capitalization
          final capitalizedStatus = status.isNotEmpty
              ? status[0].toUpperCase() + status.substring(1).toLowerCase()
              : 'Unknown';
          debugPrint('📝 Status "$status" -> "$capitalizedStatus" (original with capitalization)');
          return capitalizedStatus;
        }
    }
  }

  // Helper method to build connector icon with enhanced network debugging
  Widget _buildConnectorIcon({
    required BuildContext context,
    required station_details.Connector connector,
    required bool isSelected,
    required Color primaryColor,
  }) {
    // Get the icon URL from either icon or iconUrl field
    final String? iconUrl = connector.icon;
    final String connectorType = connector.type ?? 'Unknown';

    // Enhanced debug logging for connector cards
    debugPrint(
        '🔍 Connector card icon - Type: $connectorType, Icon URL: $iconUrl');

    // Validate URL format and accessibility
    if (iconUrl != null && iconUrl.isNotEmpty) {
      debugPrint('🌐 Attempting to load connector icon from: $iconUrl');
      debugPrint(
          '🔗 URL validation - starts with http: ${iconUrl.startsWith('http')}');
      debugPrint(
          '🔗 URL validation - contains domain: ${iconUrl.contains('.')}');

      // Additional URL validation
      try {
        final uri = Uri.parse(iconUrl);
        debugPrint(
            '🔗 URL parsing successful - Host: ${uri.host}, Path: ${uri.path}');
        debugPrint('🔗 URL scheme: ${uri.scheme}');
      } catch (e) {
        debugPrint('❌ URL parsing failed: $e');
        debugPrint('🔧 Using fallback icon due to invalid URL format');
        return _buildFallbackConnectorIcon(connectorType, primaryColor);
      }

      // Apply the SAME working mechanism from bottom section
      return iconUrl.toLowerCase().endsWith('.svg')
          ? SvgPicture.network(
              iconUrl,
              width: 28,
              height: 28,
              placeholderBuilder: (BuildContext context) => SizedBox(
                width: 28,
                height: 28,
                child: Center(
                  child: SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(primaryColor),
                    ),
                  ),
                ),
              ),
            )
          : Image.network(
              iconUrl,
              width: 28,
              height: 28,
              fit: BoxFit.contain,
              loadingBuilder: (context, child, loadingProgress) {
                if (loadingProgress == null) {
                  debugPrint(
                      '✅ Successfully loaded connector icon from: $iconUrl');
                  return child;
                }
                debugPrint(
                    '⏳ Loading connector icon... Progress: ${loadingProgress.cumulativeBytesLoaded}/${loadingProgress.expectedTotalBytes ?? 'unknown'} bytes');
                return SizedBox(
                  width: 28,
                  height: 28,
                  child: Center(
                    child: SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(primaryColor),
                      ),
                    ),
                  ),
                );
              },
              errorBuilder: (context, error, stackTrace) {
                debugPrint(
                    '❌ NETWORK ERROR loading connector card icon from $iconUrl');
                debugPrint('❌ Error details: $error');
                debugPrint('❌ Error type: ${error.runtimeType}');
                if (stackTrace != null) {
                  debugPrint(
                      '❌ Stack trace: ${stackTrace.toString().split('\n').take(3).join('\n')}');
                }

                // Try to identify specific error types
                if (error.toString().contains('404')) {
                  debugPrint('❌ HTTP 404: Icon URL not found on server');
                } else if (error.toString().contains('timeout')) {
                  debugPrint('❌ TIMEOUT: Network request timed out');
                } else if (error.toString().contains('SocketException')) {
                  debugPrint(
                      '❌ NETWORK: No internet connection or DNS resolution failed');
                } else if (error.toString().contains('HandshakeException')) {
                  debugPrint('❌ SSL: Certificate or HTTPS handshake failed');
                } else {
                  debugPrint('❌ UNKNOWN: Unidentified network error');
                }

                return _buildFallbackConnectorIcon(connectorType, primaryColor);
              },
            );
    }

    // Fallback to default icon
    debugPrint(
        '🔧 Using fallback icon for connector type: $connectorType (no valid URL)');
    return _buildFallbackConnectorIcon(connectorType, primaryColor);
  }

  // Helper method to build fallback icon for connector cards
  Widget _buildFallbackConnectorIcon(String connectorType, Color primaryColor) {
    IconData iconData;

    // Choose icon based on connector type
    switch (connectorType.toLowerCase()) {
      case 'ccs2':
      case 'ccs':
        iconData = Icons.flash_on;
        break;
      case 'chademo':
        iconData = Icons.electrical_services;
        break;
      case 'type2':
      case 'type 2':
        iconData = Icons.power;
        break;
      case 'gb/t':
      case 'gbt':
        iconData = Icons.bolt;
        break;
      default:
        iconData = Icons.electric_bolt;
    }

    return Icon(
      iconData,
      color: primaryColor,
      size: 24,
    );
  }

  // Removed unused _buildFallbackIcon method

  // Removed unused _getConnectorIconByType method

  //
// SECTION 3: BOTTOM BAR AND ACTIONS
//

  /// Enhanced bottom bar with modern design, animated button, and dark mode support
  /// Shows the selected connector and charge button
  Widget _buildSelectConnectorBar(
      BuildContext context, station_details.Connector? selectedConnector) {
    // Use the existing _isConnectorSelectable method for consistent logic
    final bool isConnectorSelectable = selectedConnector != null &&
        _isConnectorSelectable(selectedConnector.status);
    final bool isDarkMode = Theme.of(context).brightness == Brightness.dark;

    // 🚨 REMOVED: Gun connection status check no longer needed for UI
    // Button text and color are now consistent regardless of gun connection status

    // Use consistent app theme green color for Start Charging button
    final String connectorType = selectedConnector?.type ?? "AC";
    final Color primaryColor =
        AppThemes.primaryColor; // Always use app's green theme
    // Use status-based color for connector icon (yellow for "gun connected", unified color for others)
    final Color connectorIconColor = selectedConnector != null
        ? _getConnectorColor(selectedConnector.status)
        : _unifiedBadgeColor;
    final Color disabledColor =
        isDarkMode ? Colors.grey.shade800 : Colors.grey.shade300;

    // Check if gun is connected for bottom bar styling
    final bool isBottomBarGunConnected = selectedConnector?.status?.toLowerCase().trim() == 'gun connected';

    // 🚨 UI FIX: Button color always uses green (AppThemes.primaryColor) when connector is selectable
    // This provides consistent green color regardless of gun connection status
    final Color buttonColor = isConnectorSelectable
        ? primaryColor // Always use green (AppThemes.primaryColor) when selectable
        : disabledColor;

    // No need for station ID reference here

    return SafeArea(
        child: Container(
      height: 72, // Reduced height for better proportions
      decoration: BoxDecoration(
        color: isDarkMode ? const Color(0xFF1A1A1A) : Colors.white,
        boxShadow: [
          BoxShadow(
            color: isDarkMode
                ? Colors.black.withAlpha(70)
                : Colors.black.withAlpha(25),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Row(
        children: [
          // Connector info section
          if (selectedConnector != null)
            Expanded(
              flex: 3,
              child: Row(
                children: [
                  // Connector icon with larger size
                  Container(
                    width: 48, // Increased size
                    height: 48, // Increased size
                    padding: const EdgeInsets.all(
                        6), // Reduced padding for larger icon
                    decoration: BoxDecoration(
                      color: isConnectorSelectable
                          ? _getBadgeColor(selectedConnector?.status).withAlpha(isDarkMode ? 40 : 20)
                          : Colors.grey.withAlpha(isDarkMode ? 40 : 20),
                      borderRadius:
                          BorderRadius.circular(12), // Slightly larger radius
                      border: isConnectorSelectable
                          ? Border.all(
                              color: _getBadgeColor(selectedConnector?.status).withAlpha(isDarkMode ? 150 : 100),
                              width: 1.5,
                            )
                          : null,
                    ),
                    child: (selectedConnector.icon != null &&
                            selectedConnector.icon!.isNotEmpty)
                        ? selectedConnector.icon!.toLowerCase().endsWith('.svg')
                            ? SvgPicture.network(
                                selectedConnector.icon!,
                                width: 36, // Increased size
                                height: 36, // Increased size
                                placeholderBuilder: (BuildContext context) =>
                                    Icon(
                                  connectorType == "DC"
                                      ? Icons.flash_on
                                      : Icons.electrical_services,
                                  color: isConnectorSelectable
                                      ? connectorIconColor
                                      : Colors.grey,
                                  size: 26, // Increased size
                                ),
                              )
                            : Image.network(
                                selectedConnector.icon!,
                                width: 36, // Increased size
                                height: 36, // Increased size
                                loadingBuilder:
                                    (context, child, loadingProgress) {
                                  if (loadingProgress == null) {
                                    debugPrint(
                                        '✅ Successfully loaded bottom bar icon from: ${selectedConnector.icon}');
                                    return child;
                                  }
                                  debugPrint(
                                      '⏳ Loading bottom bar icon... Progress: ${loadingProgress.cumulativeBytesLoaded}/${loadingProgress.expectedTotalBytes ?? 'unknown'} bytes');
                                  return SizedBox(
                                    width: 36,
                                    height: 36,
                                    child: Center(
                                      child: SizedBox(
                                        width: 20,
                                        height: 20,
                                        child: CircularProgressIndicator(
                                          strokeWidth: 2,
                                          valueColor:
                                              AlwaysStoppedAnimation<Color>(
                                                  connectorIconColor),
                                        ),
                                      ),
                                    ),
                                  );
                                },
                                errorBuilder: (context, error, stackTrace) {
                                  debugPrint(
                                      '❌ NETWORK ERROR loading bottom bar icon from ${selectedConnector.icon}');
                                  debugPrint('❌ Error details: $error');
                                  debugPrint(
                                      '❌ Error type: ${error.runtimeType}');

                                  // Try to identify specific error types
                                  if (error.toString().contains('404')) {
                                    debugPrint(
                                        '❌ HTTP 404: Bottom bar icon URL not found on server');
                                  } else if (error
                                      .toString()
                                      .contains('timeout')) {
                                    debugPrint(
                                        '❌ TIMEOUT: Network request timed out');
                                  } else if (error
                                      .toString()
                                      .contains('SocketException')) {
                                    debugPrint(
                                        '❌ NETWORK: No internet connection or DNS resolution failed');
                                  } else if (error
                                      .toString()
                                      .contains('HandshakeException')) {
                                    debugPrint(
                                        '❌ SSL: Certificate or HTTPS handshake failed');
                                  } else {
                                    debugPrint(
                                        '❌ UNKNOWN: Unidentified network error');
                                  }

                                  return Icon(
                                    connectorType == "DC"
                                        ? Icons.flash_on
                                        : Icons.electrical_services,
                                    color: isConnectorSelectable
                                        ? connectorIconColor
                                        : Colors.grey,
                                    size: 26, // Increased size
                                  );
                                },
                              )
                        : Icon(
                            connectorType == "DC"
                                ? Icons.flash_on
                                : Icons.electrical_services,
                            color: isConnectorSelectable
                                ? connectorIconColor
                                : Colors.grey,
                            size: 26, // Increased size
                          ),
                  ),
                  const SizedBox(width: 12),
                  // Connector details with improved text styling
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          _selectedConnector!.label ??
                              _selectedConnector!.type ??
                              'Connector',
                          style: TextStyle(
                            fontWeight: FontWeight.w600, // Slightly lighter
                            fontSize: 14, // Slightly smaller
                            overflow: TextOverflow.ellipsis,
                            color: isDarkMode ? Colors.white : Colors.black87,
                          ),
                          maxLines: 1,
                        ),
                        const SizedBox(height: 3), // Slightly more spacing
                        Text(
                          isConnectorSelectable
                              ? ConnectorUtils.buildConnectorPowerText(
                                  _selectedConnector!)
                              : 'Connector unavailable',
                          style: TextStyle(
                            fontSize: 12, // Slightly smaller
                            color: isConnectorSelectable
                                ? connectorIconColor
                                : Colors.grey,
                          ),
                          maxLines: 1,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            )
          else
            Expanded(
              flex: 3,
              child: Text(
                'Select a connector to start charging',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: isDarkMode ? Colors.grey.shade400 : Colors.grey,
                ),
              ),
            ),

          // Spacer for better separation
          const SizedBox(width: 8),

          // Action button with improved styling
          Expanded(
            flex: 2,
            child: GestureDetector(
              onTap: () {
                if (isConnectorSelectable) {
                  // 🚨 CRITICAL FIX: Bypass gun connection check for normal charging flow
                  // Allow users to proceed to charging options page directly
                  // Gun connection will be verified during the actual charging process
                  debugPrint(
                      '🔌 CONNECTOR SELECTED: Proceeding to charging options');
                  debugPrint(
                      '🔌 Connector ID: ${selectedConnector?.connectorId}');
                  debugPrint('🔌 Connector Type: ${selectedConnector?.type}');
                  debugPrint(
                      '🔌 Bypassing gun connection check for normal flow');

                  _handleStartChargingTap();

                  // REMOVED: Gun connection check that was blocking navigation
                  // Original blocking code:
                  // if (_isGunConnected(selectedConnector)) {
                  //   _handleStartChargingTap();
                  // } else {
                  //   ScaffoldMessenger.of(context).showSnackBar(...);
                  // }
                } else {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content:
                          const Text('Please select an available connector'),
                      behavior: SnackBarBehavior.floating,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                  );
                }
              },
              child: AnimatedContainer(
                duration: const Duration(milliseconds: 300),
                curve: Curves.easeInOut,
                height: 48, // Fixed height for better proportions
                decoration: BoxDecoration(
                  color: buttonColor,
                  borderRadius: BorderRadius.circular(12), // Smaller radius
                  boxShadow: isConnectorSelectable
                      ? [
                          BoxShadow(
                            color: buttonColor.withAlpha(isDarkMode ? 100 : 80),
                            blurRadius: 8, // Smaller blur
                            offset: const Offset(0, 3), // Smaller offset
                          )
                        ]
                      : null,
                ),
                child: Center(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // Show only one icon - bolt for normal charging, flash_on for instant charging
                      if (isConnectorSelectable)
                        Icon(
                          _instantCharging == 1 ? Icons.flash_on : Icons.bolt,
                          size: 18, // Smaller icon
                          color: Colors.white,
                        ),
                      const SizedBox(width: 6), // Smaller spacing
                      Text(
                        // Show different text based on instant charging mode
                        _instantCharging == 1 ? 'Instant Charging' : 'Start Charging',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 14, // Smaller text
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    ));
  }

  /// Validates wallet balance for charging (minimum ₹1 required)
  /// Returns true if balance is sufficient, false otherwise
  bool _validateWalletBalance() {
    if (_walletData?.balance == null) {
      debugPrint('Wallet data not available');
      return false;
    }

    const double minimumBalance = 1.0; // Changed from 50.0 to 1.0
    final double currentBalance = _walletData!.balance!;

    debugPrint(
        'Wallet balance validation: ₹$currentBalance (minimum: ₹$minimumBalance)');
    // Allow charging if balance is greater than minimum required
    return currentBalance > minimumBalance;
  }

  /// Shows low balance dialog when wallet balance is insufficient
  /// Professional minimalist design with consistent app theming
  void _showLowBalanceDialog() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          backgroundColor: Colors.transparent,
          elevation: 0,
          child: Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20),
              color: isDarkMode ? AppThemes.darkCard : Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withAlpha(isDarkMode ? 60 : 15),
                  blurRadius: 20,
                  offset: const Offset(0, 10),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Minimalist wallet icon with app's primary color
                Container(
                  width: 64,
                  height: 64,
                  decoration: BoxDecoration(
                    color: AppThemes.primaryColor.withAlpha(20),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    LineIcons.wallet, // Using same icon as navigation bar
                    size: 32,
                    color: AppThemes.primaryColor,
                  ),
                ),

                const SizedBox(height: 20),

                // Clean title typography
                Text(
                  'Insufficient Balance',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.w600,
                    color: isDarkMode
                        ? AppThemes.darkTextPrimary
                        : AppThemes.lightTextPrimary,
                  ),
                ),

                const SizedBox(height: 8),

                // Subtitle for context
                Text(
                  'Add money to your wallet to start charging',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: 14,
                    color: isDarkMode
                        ? AppThemes.darkTextSecondary
                        : AppThemes.lightTextSecondary,
                  ),
                ),

                const SizedBox(height: 24),

                // Clean balance display
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: isDarkMode
                        ? AppThemes.darkSurface
                        : Colors.grey.shade50,
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: isDarkMode
                          ? AppThemes.darkBorder
                          : Colors.grey.shade200,
                      width: 1,
                    ),
                  ),
                  child: Column(
                    children: [
                      Text(
                        'Current Balance',
                        style: TextStyle(
                          fontSize: 13,
                          fontWeight: FontWeight.w500,
                          color: isDarkMode
                              ? AppThemes.darkTextSecondary
                              : AppThemes.lightTextSecondary,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        '₹${_walletData?.balance?.toStringAsFixed(2) ?? '0.00'}',
                        style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: isDarkMode
                              ? AppThemes.darkTextPrimary
                              : AppThemes.lightTextPrimary,
                        ),
                      ),
                      const SizedBox(height: 12),
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 6),
                        decoration: BoxDecoration(
                          color: Colors.orange.withAlpha(20),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          'Minimum ₹50 required',
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                            color: Colors.orange.shade700,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 24),

                // Clean action buttons
                Row(
                  children: [
                    // Cancel button
                    Expanded(
                      child: OutlinedButton(
                        onPressed: () => Navigator.of(context).pop(),
                        style: OutlinedButton.styleFrom(
                          foregroundColor: isDarkMode
                              ? AppThemes.darkTextSecondary
                              : AppThemes.lightTextSecondary,
                          side: BorderSide(
                            color: isDarkMode
                                ? AppThemes.darkBorder
                                : Colors.grey.shade300,
                            width: 1,
                          ),
                          padding: const EdgeInsets.symmetric(vertical: 14),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        child: const Text(
                          'Cancel',
                          style: TextStyle(
                            fontSize: 15,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),

                    const SizedBox(width: 12),

                    // Add Money button with app's primary color
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: () {
                          Navigator.of(context).pop(); // Close dialog
                          _navigateToWallet(); // Navigate to wallet
                        },
                        icon: Icon(
                          LineIcons.plus, // Consistent with LineIcons theme
                          size: 18,
                          color: Colors.white,
                        ),
                        label: const Text(
                          'Add Money',
                          style: TextStyle(
                            fontSize: 15,
                            fontWeight: FontWeight.w600,
                            color: Colors.white,
                          ),
                        ),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppThemes.primaryColor,
                          foregroundColor: Colors.white,
                          elevation: 0,
                          padding: const EdgeInsets.symmetric(vertical: 14),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// Navigates to the wallet page for adding money
  void _navigateToWallet() {
    Navigator.pushNamed(context, '/wallet');
  }

  /// Handles the "Start Charging" button tap with wallet validation and instant charging logic
  void _handleStartChargingTap() {
    debugPrint('=== START CHARGING TAP HANDLER ===');
    debugPrint('Wallet balance: ${_walletData?.balance}');
    debugPrint('Instant charging: $_instantCharging');
    debugPrint('Selected connector: ${_selectedConnector?.type}');

    // 🚨 CRITICAL: Prevent multiple simultaneous charging flows
    if (_isChargingButtonProcessing) {
      debugPrint('🛡️ Charging button already processing - ignoring duplicate tap');
      return;
    }

    // Mark button as processing
    _isChargingButtonProcessing = true;

    // Step 1: Validate wallet balance (minimum ₹50)
    if (!_validateWalletBalance()) {
      debugPrint('❌ Wallet balance insufficient - showing low balance dialog');
      _isChargingButtonProcessing = false; // Reset flag
      _showLowBalanceDialog();
      return;
    }

    debugPrint('✅ Wallet balance sufficient - proceeding with charging flow');

    // 🚨 CRITICAL: Stop background refresh before charging flow begins
    // This prevents race conditions between background refresh and charging APIs
    _stopBackgroundRefreshForCharging();

    // Step 2: Check instant charging flag and navigate accordingly
    if (_instantCharging == 1) {
      debugPrint(
          '🚀 Instant charging enabled - navigating directly to initialization');

      // Show instant charging feedback to user
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(
                Icons.flash_on,
                color: Colors.white,
                size: 20,
              ),
              const SizedBox(width: 12),
              const Expanded(
                child: Text(
                  'Instant charging enabled - Starting session directly',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          backgroundColor: const Color(0xFFFFA000), // Amber color for instant charging
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          duration: const Duration(seconds: 2),
          margin: const EdgeInsets.all(12),
        ),
      );

      _navigateToChargingInitialization();
    } else {
      debugPrint('⚙️ Instant charging disabled - showing charging options');

      // 🚨 CRITICAL: Add small delay to prevent rapid navigation issues
      Future.delayed(const Duration(milliseconds: 100), () {
        if (mounted) {
          _showChargingBottomSheet();
        } else {
          _isChargingButtonProcessing = false; // Reset flag if widget disposed
        }
      });
    }
  }

  /// Navigates directly to charging initialization screen for instant charging
  void _navigateToChargingInitialization() async {
    // CRITICAL FIX: Check if widget is still mounted and context is valid
    if (!mounted) {
      debugPrint('❌ Widget not mounted - cannot navigate to charging initialization');
      _isChargingButtonProcessing = false; // Reset flag
      return;
    }

    if (_selectedConnector == null) {
      debugPrint('❌ No connector selected for instant charging');
      _isChargingButtonProcessing = false; // Reset flag
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Please select a connector first'),
            backgroundColor: Colors.red,
            duration: Duration(seconds: 2),
          ),
        );
      }
      return;
    }

    final connector = _selectedConnector!;
    debugPrint('🔌 ===== INSTANT CHARGING INITIALIZATION =====');
    debugPrint('🔌 Starting instant charging with connector: ${connector.type}');
    debugPrint('🔌 EVSE UID: ${connector.evsesUid}');
    debugPrint('🔌 Connector ID: ${connector.connectorId}');
    debugPrint('🔌 Connector Price: ₹${connector.pricePerUnit}/kWh');

    // Extract wallet balance and price per unit for calculation
    final double walletBalance = _walletData?.balance?.toDouble() ?? 0.0;
    final double pricePerUnit = connector.pricePerUnit?.toDouble() ?? 20.0;

    // Validate required data
    if (walletBalance <= 0) {
      debugPrint('❌ Invalid wallet balance for instant charging: $walletBalance');
      _isChargingButtonProcessing = false; // Reset flag
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Wallet balance not available. Please refresh and try again.'),
          backgroundColor: Colors.red,
          duration: Duration(seconds: 3),
        ),
      );
      return;
    }

    if (pricePerUnit <= 0) {
      debugPrint('❌ Invalid price per unit for instant charging: $pricePerUnit');
      _isChargingButtonProcessing = false; // Reset flag
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Connector pricing not available. Please try again.'),
          backgroundColor: Colors.red,
          duration: Duration(seconds: 3),
        ),
      );
      return;
    }

    // CRITICAL FIX: Apply same balance calculation logic as charging options page
    // Use 82% of wallet balance to account for 18% GST (matching charging_options_page.dart)
    const double usableWalletPercentage = 82.0; // Same as charging options page
    final double maxUsableAmount = (walletBalance * usableWalletPercentage / 100);

    // 🚨 CRITICAL FIX: Calculate units from amount (just like normal mode does)
    // Backend expects units when instantCharging: true, not monetary amount
    final double calculatedUnits = maxUsableAmount / pricePerUnit;

    debugPrint('🔌 INSTANT CHARGING - BALANCE CALCULATION MATCH:');
    debugPrint('🔌   - Full Wallet Balance: ₹${walletBalance.toStringAsFixed(2)}');
    debugPrint('🔌   - Usable Amount (82%): ₹${maxUsableAmount.toStringAsFixed(2)}');
    debugPrint('🔌   - Price per Unit: ₹${pricePerUnit.toStringAsFixed(2)}/kWh');
    debugPrint('🔌   - Calculated Units: ${calculatedUnits.toStringAsFixed(2)} kWh');
    debugPrint('🔌   - Charge Type: units (always units for backend)');
    debugPrint('🔌   - Instant Charging: true (backend will use calculated units)');

    // Store instant charging parameters in global service
    final chargingParamsService = ChargingParametersService();
    final instantChargingParams = {
      'chargingValue': calculatedUnits, // 🚨 CRITICAL FIX: Use calculated units (kWh), not monetary amount
      'chargeType': 'units', // Always 'units' as per backend logic
      'pricePerUnit': pricePerUnit, // Connector's price per unit
      'maxPower': connector.maxElectricPower?.toDouble() ?? 30.0,
      'connectorType': connector.type ?? 'Unknown',
      'evsesUid': connector.evsesUid ?? '',
      'connectorId': connector.connectorId ?? '',
      'instantCharging': true, // Backend checks this flag
      'walletBalance': walletBalance, // For reference (full balance)
    };

    chargingParamsService.storeChargingParameters(instantChargingParams);

    debugPrint('✅ INSTANT CHARGING: Stored parameters in global service');
    debugPrint('✅ Parameters: $instantChargingParams');

    // 🚨 CRITICAL FIX: Start transaction first (align with normal mode flow)
    debugPrint('🚀 ===== INSTANT MODE: STARTING TRANSACTION FIRST =====');
    debugPrint('🚀 This aligns instant mode with normal mode flow');
    debugPrint('🚀 Step 1: Start transaction → Step 2: Navigate with sessionAlreadyStarted: true');

    try {
      // Show loading indicator
      if (mounted) {
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) => const Center(
            child: CircularProgressIndicator(),
          ),
        );
      }

      // Start the charging session (Step 1)
      final chargingService = ChargingSessionService();
      final startResponse = await chargingService.startChargingSession(
        evseUid: connector.evsesUid ?? '',
        connectorId: connector.connectorId ?? '',
        chargingValue: calculatedUnits, // 🚨 CRITICAL FIX: Use calculated units, not monetary amount
        instantCharging: true,
      );

      // Dismiss loading dialog
      if (mounted && Navigator.canPop(context)) {
        Navigator.pop(context);
      }

      if (startResponse.success && startResponse.data != null) {
        // Extract and store transaction ID
        final transactionId = startResponse.data!['id']?.toString();
        if (transactionId != null && transactionId.isNotEmpty) {
          chargingParamsService.storeTransactionId(transactionId);
          debugPrint('🔒 INSTANT MODE: Stored transaction ID: $transactionId');

          // Navigate to initialization screen with sessionAlreadyStarted: true
          debugPrint('🚀 INSTANT MODE: Navigating with sessionAlreadyStarted: true');
          if (mounted) {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => ChargingInitializationScreen(
                  stationUid: connector.evsesUid ?? _station?.uid ?? '',
                  connectorId: connector.connectorId ?? '',
                  sessionAlreadyStarted: true, // 🚨 CRITICAL FIX: Session already started
                ),
              ),
            );
            // Reset flag after successful navigation
            _isChargingButtonProcessing = false;
          }
        } else {
          debugPrint('❌ INSTANT MODE: No transaction ID in response');
          _isChargingButtonProcessing = false; // Reset flag
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Failed to start charging session. Please try again.'),
                backgroundColor: Colors.red,
                duration: Duration(seconds: 3),
              ),
            );
          }
        }
      } else {
        debugPrint('❌ INSTANT MODE: Failed to start charging session');
        debugPrint('❌ Error: ${startResponse.message}');
        _isChargingButtonProcessing = false; // Reset flag
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Failed to start charging: ${startResponse.message}'),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 3),
            ),
          );
        }
      }
    } catch (e) {
      debugPrint('❌ INSTANT MODE: Exception during transaction start: $e');
      _isChargingButtonProcessing = false; // Reset flag

      // Dismiss loading dialog if still showing
      if (mounted && Navigator.canPop(context)) {
        Navigator.pop(context);
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error starting charging: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  /// Shows the charging options bottom sheet for the selected connector
  /// This method delegates to the ChargingOptionsPage for a cleaner separation of concerns
  void _showChargingBottomSheet() {
    // 🚨 CRITICAL: Check if widget is still mounted before using context
    if (!mounted) {
      debugPrint('❌ Widget not mounted - cannot show charging bottom sheet');
      _isChargingButtonProcessing = false; // Reset flag
      return;
    }

    if (_selectedConnector == null) {
      _isChargingButtonProcessing = false; // Reset flag
      return;
    }

    // Get the selected connector
    final connector = _selectedConnector!;

    // Debug log the original connector data
    debugPrint('🔍 ===== CHARGING BOTTOM SHEET DEBUG =====');
    debugPrint('🔍 Original connector EVSE UID: "${connector.evsesUid}"');
    debugPrint('🔍 Original connector ID: "${connector.connectorId}"');
    debugPrint('🔍 Original connector type: "${connector.type}"');

    // Use the toMainConnector method to convert station_details.Connector to station_model.Connector
    // This ensures we're using the proper conversion logic defined in the model
    final mainConnector = connector.toMainConnector();

    // Debug log the converted connector data
    debugPrint('🔍 Converted connector EVSE UID: "${mainConnector.evsesUid}"');
    debugPrint('🔍 Converted connector ID: "${mainConnector.id}"');
    debugPrint('🔍 Converted connector type: "${mainConnector.type}"');

    // CRITICAL FIX: Always use the original connector's EVSE UID if available
    // The station UID should NEVER be used as EVSE UID for charging operations
    String finalEvseUid = connector.evsesUid ?? '';

    if (finalEvseUid.isEmpty) {
      debugPrint(
          '❌ CRITICAL ERROR: Connector has no EVSE UID - this should not happen');
      debugPrint('❌ Station UID: ${_station?.uid}');
      debugPrint(
          '❌ This indicates a data processing issue in the station details API');

      // Show error to user instead of using wrong UID
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text(
                '❌ Error: Connector data is incomplete. Please try again.'),
            backgroundColor: Colors.red,
            duration: Duration(seconds: 3),
          ),
        );
      }
      return;
    }

    // Create the final connector with the correct EVSE UID
    final finalConnector = Connector(
      id: mainConnector.id,
      name: mainConnector.name,
      type: mainConnector.type,
      price: mainConnector.price,
      power: mainConnector.power,
      totalGuns: mainConnector.totalGuns,
      availableGuns: mainConnector.availableGuns,
      icon: mainConnector.icon,
      status: mainConnector.status,
      maxElectricPower: mainConnector.maxElectricPower,
      standard: mainConnector.standard,
      priceLabel: mainConnector.priceLabel,
      pricePerUnit: mainConnector.pricePerUnit,
      soc: mainConnector.soc,
      powerOutput: mainConnector.powerOutput,
      maxPower: mainConnector.maxPower,
      evsesUid:
          finalEvseUid, // Use the correct EVSE UID from original connector
    );

    // Debug log the final connector
    debugPrint('✅ Final connector EVSE UID: "${finalConnector.evsesUid}"');
    debugPrint('✅ Final connector ID: "${finalConnector.id}"');
    debugPrint('✅ Final connector type: "${finalConnector.type}"');

    // 🚨 CRITICAL: Final mounted check before showing modal bottom sheet
    if (!mounted) {
      debugPrint('❌ Widget not mounted - cannot show modal bottom sheet');
      _isChargingButtonProcessing = false; // Reset flag
      return;
    }

    // Show the charging options page with the correct connector
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return ChargingOptionsPage(connector: finalConnector);
      },
    ).then((_) {
      // Reset flag when bottom sheet is dismissed
      _isChargingButtonProcessing = false;
    });
  }

  // Get power type from connector-specific power_type field (prioritize connector data over EVSE data)
  String _getPowerTypeFromEvse(station_details.Connector connector) {
    // PRIORITY 1: Use connector-specific power_type if available
    if (connector.powerOutput != null && connector.powerOutput!.isNotEmpty) {
      debugPrint(
          '✅ USING CONNECTOR-SPECIFIC power_type for connector ${connector.connectorId}: ${connector.powerOutput}');

      // Parse connector-specific power type string (e.g., "DC", "AC")
      switch (connector.powerOutput!.toUpperCase()) {
        case 'DCAC':
          return 'DC+AC';
        case 'DC':
          return 'DC';
        case 'AC':
          return 'AC';
        default:
          return connector.powerOutput!.toUpperCase();
      }
    }

    // FALLBACK: Use EVSE-level power output if connector-specific data is not available
    debugPrint(
        '❌ NO CONNECTOR-SPECIFIC power_type found for connector ${connector.connectorId}, falling back to EVSE power_output');

    if (connector.evsesUid == null || connector.evsesUid!.isEmpty) {
      debugPrint('❌ No EVSE UID available for fallback');
      return '';
    }

    final powerOutput = _evsePowerOutputMap[connector.evsesUid];
    if (powerOutput == null || powerOutput.isEmpty) {
      return '';
    }

    debugPrint(
        '⚠️ FALLBACK: Using EVSE-level power_output for connector ${connector.connectorId} from EVSE ${connector.evsesUid}: $powerOutput');

    // Parse EVSE power output string (e.g., "DCAC", "DC", "AC")
    switch (powerOutput.toUpperCase()) {
      case 'DCAC':
        return 'DC+AC';
      case 'DC':
        return 'DC';
      case 'AC':
        return 'AC';
      default:
        return powerOutput.toUpperCase();
    }
  }



  /*
  TEMPORARILY DISABLED: Helper method for cleaning station names and power type

  This method was used to clean station names before passing them to the
  analytics page. Since analytics is temporarily disabled, this method
  is also commented out to avoid unused code warnings.

  To re-enable: Uncomment this method when analytics feature is restored.
  */

  // Helper method to clean station name by removing unwanted text labels
  /* String _cleanStationName(String stationName) {
    // Remove the specific unwanted text pattern "IN*EPL010155, SOMETHING TEXT LABEL"
    // This pattern appears to be some kind of internal identifier or test data
    String cleaned = stationName;

    // Remove patterns like "IN*EPL010155" (alphanumeric codes with asterisks)
    cleaned = cleaned.replaceAll(RegExp(r'IN\*[A-Z0-9]+'), '');

    // Remove common unwanted text patterns
    cleaned = cleaned.replaceAll(RegExp(r',?\s*SOMETHING TEXT LABEL', caseSensitive: false), '');

    // Clean up any remaining commas, extra spaces, or leading/trailing whitespace
    cleaned = cleaned.replaceAll(RegExp(r',\s*,'), ','); // Remove double commas
    cleaned = cleaned.replaceAll(RegExp(r'^,\s*'), ''); // Remove leading comma
    cleaned = cleaned.replaceAll(RegExp(r',\s*$'), ''); // Remove trailing comma
    cleaned = cleaned.replaceAll(RegExp(r'\s+'), ' '); // Replace multiple spaces with single space
    cleaned = cleaned.trim(); // Remove leading/trailing whitespace

    // If the cleaned name is empty, return a default
    if (cleaned.isEmpty) {
      return 'Station';
    }

    return cleaned;
  } */
}
